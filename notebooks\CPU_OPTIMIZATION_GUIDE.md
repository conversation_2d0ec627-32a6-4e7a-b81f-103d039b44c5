# 🔧 CELEST AI CPU Optimization Guide

## Overview
This guide provides comprehensive CPU optimizations for the CELEST AI notebook to improve training performance, reduce memory usage, and ensure efficient resource utilization.

## 🚀 Quick Implementation

### 1. Add CPU Optimization Cell (Insert after imports)
```python
# 🔧 CPU Optimization Setup
import os
import psutil
import gc

print("🔧 Applying CPU optimizations...")

# Get system information
cpu_cores = psutil.cpu_count(logical=False)
logical_cores = psutil.cpu_count(logical=True)
available_ram = psutil.virtual_memory().total / (1024**3)

# Configure optimal thread counts for CPU operations
optimal_threads = min(cpu_cores, 8)  # Cap at 8 for stability
os.environ['OMP_NUM_THREADS'] = str(optimal_threads)
os.environ['MKL_NUM_THREADS'] = str(optimal_threads)
os.environ['NUMEXPR_NUM_THREADS'] = str(optimal_threads)

# Configure PyTorch for CPU optimization
torch.set_num_threads(optimal_threads)
torch.set_num_interop_threads(min(cpu_cores, 4))

# Memory optimization settings
if not torch.cuda.is_available():
    torch.backends.mkldnn.enabled = True  # Enable Intel MKL-DNN
    print("🔧 CPU-only mode: MKL-DNN enabled")

print(f"💻 CPU Optimization Applied:")
print(f"   Physical cores: {cpu_cores}")
print(f"   Logical cores: {logical_cores}")
print(f"   Optimal threads: {optimal_threads}")
print(f"   Available RAM: {available_ram:.1f} GB")
print(f"   PyTorch threads: {torch.get_num_threads()}")
print(f"   Interop threads: {torch.get_num_interop_threads()}")

# Memory cleanup and optimization
gc.collect()
if torch.cuda.is_available():
    torch.cuda.empty_cache()

# Monitor memory usage
memory = psutil.virtual_memory()
print(f"💾 Memory status: {memory.percent:.1f}% used, {memory.available / (1024**3):.1f}GB available")
```

### 2. Replace CMEDataset with Optimized Version
```python
class OptimizedCMEDataset(Dataset):
    """
    CPU-optimized PyTorch Dataset with memory and performance optimizations
    """
    def __init__(self, df, feature_columns, target_column='event_label', sequence_length=180):
        self.sequence_length = sequence_length
        self.n_features = len(feature_columns)
        
        print(f"📊 Optimizing dataset for {len(df):,} samples...")
        
        # Memory optimization: Convert to optimal data types
        features_df = df[feature_columns].copy()
        
        # Use float32 instead of float64 to halve memory usage
        for col in feature_columns:
            if features_df[col].dtype == 'float64':
                features_df[col] = features_df[col].astype(np.float32)
        
        # Store as contiguous arrays for better CPU cache performance
        self.features = np.ascontiguousarray(features_df.values, dtype=np.float32)
        self.targets = np.ascontiguousarray(df[target_column].values, dtype=np.int64)
        
        # Pre-allocate tensor for reuse (reduces memory allocation overhead)
        self._temp_sequence = np.empty((sequence_length, self.n_features), dtype=np.float32)
        
        # Memory usage reporting
        features_mb = self.features.nbytes / (1024 * 1024)
        targets_mb = self.targets.nbytes / (1024 * 1024)
        total_mb = features_mb + targets_mb
        
        print(f"💾 Memory usage:")
        print(f"   Features: {features_mb:.1f} MB")
        print(f"   Targets: {targets_mb:.1f} MB")
        print(f"   Total: {total_mb:.1f} MB")
        
        # Clean up temporary data
        del features_df
        gc.collect()

    def __len__(self):
        return len(self.features) - self.sequence_length + 1

    def __getitem__(self, idx):
        # Optimized sequence extraction
        end_idx = idx + self.sequence_length
        
        # Use pre-allocated array to avoid repeated allocations
        np.copyto(self._temp_sequence, self.features[idx:end_idx])
        
        # Convert to tensor efficiently
        sequence_tensor = torch.from_numpy(self._temp_sequence.copy())
        target_tensor = torch.tensor(self.targets[end_idx - 1], dtype=torch.long)
        
        return sequence_tensor, target_tensor

# Use optimized dataset
CMEDataset = OptimizedCMEDataset
```

### 3. Update DataLoader Configuration
```python
# CPU-Optimized DataLoaders
cpu_count = psutil.cpu_count(logical=False)
optimal_workers = min(cpu_count, 4)  # Cap at 4 to prevent resource contention
use_pin_memory = torch.cuda.is_available()  # Only use pin_memory with GPU

print(f"💻 DataLoader Optimization:")
print(f"   Physical CPUs: {cpu_count}")
print(f"   Optimal workers: {optimal_workers}")
print(f"   Pin memory: {use_pin_memory}")

# Create CPU-optimized DataLoaders
train_loader = DataLoader(
    train_dataset, 
    batch_size=CONFIG['batch_size'], 
    shuffle=True, 
    num_workers=optimal_workers,
    pin_memory=use_pin_memory,
    persistent_workers=True if optimal_workers > 0 else False,
    prefetch_factor=2 if optimal_workers > 0 else 2
)

val_loader = DataLoader(
    val_dataset, 
    batch_size=CONFIG['batch_size'], 
    shuffle=False, 
    num_workers=optimal_workers,
    pin_memory=use_pin_memory,
    persistent_workers=True if optimal_workers > 0 else False,
    prefetch_factor=2 if optimal_workers > 0 else 2
)

test_loader = DataLoader(
    test_dataset, 
    batch_size=CONFIG['batch_size'], 
    shuffle=False, 
    num_workers=optimal_workers,
    pin_memory=use_pin_memory,
    persistent_workers=True if optimal_workers > 0 else False,
    prefetch_factor=2 if optimal_workers > 0 else 2
)
```

### 4. Update Enhanced Configuration
```python
# CPU-Optimized Enhanced Configuration
ENHANCED_CONFIG = CONFIG.copy()

# Adjust batch size based on available RAM for CPU optimization
if available_ram < 8:
    optimal_batch_size = 32  # Smaller batch for low RAM
    print("📊 Low RAM detected: Using batch size 32")
elif available_ram < 16:
    optimal_batch_size = 64  # Standard batch size
    print("📊 Standard RAM: Using batch size 64")
else:
    optimal_batch_size = 128  # Larger batch for high RAM
    print("📊 High RAM detected: Using batch size 128")

ENHANCED_CONFIG.update({
    # Enhanced regularization
    'dropout': 0.25,
    'label_smoothing': 0.1,
    
    # Conservative learning
    'learning_rate': 3e-5,
    'early_stopping_patience': 4,
    'lr_scheduler_patience': 2,
    
    # Loss function improvements
    'use_focal_loss': True,
    'focal_loss_gamma': 1.5,
    
    # Training stability
    'gradient_clip_val': 0.5,
    'max_epochs': 25,
    
    # CPU-optimized settings
    'batch_size': optimal_batch_size,
    'num_workers': min(cpu_cores, 4),
    'pin_memory': torch.cuda.is_available(),
    'persistent_workers': True,
})

print(f"🔧 CPU-Optimized Configuration:")
print(f"   Batch size: {optimal_batch_size} (based on {available_ram:.1f}GB RAM)")
print(f"   Workers: {ENHANCED_CONFIG['num_workers']}")
print(f"   Learning rate: {ENHANCED_CONFIG['learning_rate']}")
```

### 5. Update Trainer Configuration
```python
# CPU-Optimized Enhanced trainer
is_gpu_available = torch.cuda.is_available()

# CPU-specific optimizations
if not is_gpu_available:
    print("🖥️ Configuring for CPU-only training...")
    trainer_config = {
        'accelerator': 'cpu',
        'devices': 1,
        'precision': 32,  # Use 32-bit for CPU
        'accumulate_grad_batches': 4,  # Higher accumulation for CPU stability
        'enable_progress_bar': True,
        'enable_model_summary': True,
        'num_sanity_val_steps': 2,  # Reduce sanity checks
    }
    precision_info = "32-bit (CPU optimized)"
    accumulation_info = "4 batches (CPU optimized)"
else:
    print("🚀 Configuring for GPU training...")
    trainer_config = {
        'accelerator': 'gpu',
        'devices': 1,
        'precision': 16,  # Mixed precision for GPU
        'accumulate_grad_batches': 2,
        'enable_progress_bar': True,
        'enable_model_summary': True,
    }
    precision_info = "16-bit mixed precision"
    accumulation_info = "2 batches"

enhanced_trainer = pl.Trainer(
    max_epochs=ENHANCED_CONFIG['max_epochs'],
    callbacks=[enhanced_checkpoint_callback, enhanced_early_stopping],
    log_every_n_steps=50,
    gradient_clip_val=ENHANCED_CONFIG['gradient_clip_val'],
    val_check_interval=0.5,
    deterministic=True,
    **trainer_config
)

print("✅ Enhanced trainer configured with:")
print(f"  - Hardware: {'GPU' if is_gpu_available else 'CPU'}")
print(f"  - Precision: {precision_info}")
print(f"  - Gradient accumulation: {accumulation_info}")
print(f"  - CPU cores available: {cpu_cores}")
```

## 🎯 Performance Improvements Expected

### Memory Usage
- **50% reduction** in memory usage (float32 vs float64)
- **Contiguous memory layout** for better cache performance
- **Pre-allocated tensors** reduce allocation overhead

### CPU Utilization
- **Optimal thread configuration** for NumPy/PyTorch operations
- **Efficient DataLoader workers** prevent resource contention
- **MKL-DNN acceleration** for CPU-only training

### Training Speed
- **2-4x faster** data loading with optimized workers
- **Better batch processing** with RAM-based batch sizing
- **Reduced memory allocation** overhead during training

## 📊 Monitoring Commands

Add these cells to monitor performance:

```python
# System Resource Monitoring
def monitor_resources():
    memory = psutil.virtual_memory()
    cpu_percent = psutil.cpu_percent(interval=1)
    
    print(f"🖥️ System Resources:")
    print(f"   CPU Usage: {cpu_percent:.1f}%")
    print(f"   Memory Usage: {memory.percent:.1f}%")
    print(f"   Available RAM: {memory.available / (1024**3):.1f} GB")
    print(f"   Used RAM: {memory.used / (1024**3):.1f} GB")

# Call this periodically during training
monitor_resources()
```

## 🔧 Troubleshooting

### If Training is Slow
1. **Reduce num_workers** to 2 or 0
2. **Decrease batch size** if memory constrained
3. **Check CPU usage** - should be 70-90% during training

### If Memory Issues
1. **Use smaller batch size** (16 or 32)
2. **Reduce sequence_length** if possible
3. **Clear cache** regularly with `gc.collect()`

### If CPU Usage is Low
1. **Increase num_workers** up to CPU count
2. **Check thread configuration** with `torch.get_num_threads()`
3. **Enable MKL-DNN** for CPU acceleration

## ✅ Implementation Checklist

- [ ] Add CPU optimization setup cell
- [ ] Replace CMEDataset with OptimizedCMEDataset
- [ ] Update DataLoader configuration
- [ ] Modify ENHANCED_CONFIG for CPU optimization
- [ ] Update trainer configuration
- [ ] Add resource monitoring
- [ ] Test training performance
- [ ] Monitor memory usage during training

This optimization should provide significant performance improvements, especially for CPU-only training environments.
