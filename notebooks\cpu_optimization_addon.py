# CELEST AI - CPU Optimization Add-on
# Add these optimizations to your existing notebook for better CPU performance

import os
import psutil
import gc
import torch
import numpy as np
from torch.utils.data import Dataset, DataLoader
import pytorch_lightning as pl

def setup_cpu_optimization():
    """
    Configure optimal CPU settings for PyTorch and system libraries
    """
    print("🔧 Setting up CPU optimizations...")
    
    # Get system information
    cpu_cores = psutil.cpu_count(logical=False)
    logical_cores = psutil.cpu_count(logical=True)
    available_ram = psutil.virtual_memory().total / (1024**3)
    
    # Configure optimal thread counts
    optimal_threads = min(cpu_cores, 8)  # Cap at 8 for stability
    
    # Set environment variables for CPU optimization
    os.environ['OMP_NUM_THREADS'] = str(optimal_threads)
    os.environ['MKL_NUM_THREADS'] = str(optimal_threads)
    os.environ['NUMEXPR_NUM_THREADS'] = str(optimal_threads)
    os.environ['OPENBLAS_NUM_THREADS'] = str(optimal_threads)
    
    # Configure PyTorch for CPU optimization
    torch.set_num_threads(optimal_threads)
    torch.set_num_interop_threads(min(cpu_cores, 4))
    
    # Enable CPU optimizations
    if not torch.cuda.is_available():
        torch.backends.mkldnn.enabled = True  # Enable Intel MKL-DNN
        print("🔧 CPU-only mode: MKL-DNN enabled")
    
    print(f"💻 CPU Configuration:")
    print(f"   Physical cores: {cpu_cores}")
    print(f"   Logical cores: {logical_cores}")
    print(f"   Optimal threads: {optimal_threads}")
    print(f"   Available RAM: {available_ram:.1f} GB")
    print(f"   PyTorch threads: {torch.get_num_threads()}")
    print(f"   Interop threads: {torch.get_num_interop_threads()}")
    
    return {
        'cpu_cores': cpu_cores,
        'optimal_threads': optimal_threads,
        'available_ram': available_ram
    }


class CPUOptimizedCMEDataset(Dataset):
    """
    CPU-optimized PyTorch Dataset with memory and performance optimizations
    """
    def __init__(self, df, feature_columns, target_column='event_label', sequence_length=180):
        self.sequence_length = sequence_length
        self.n_features = len(feature_columns)
        
        print(f"📊 Optimizing dataset for {len(df):,} samples...")
        
        # Memory optimization: Convert to optimal data types
        features_df = df[feature_columns].copy()
        
        # Use float32 instead of float64 to halve memory usage
        for col in feature_columns:
            if features_df[col].dtype == 'float64':
                features_df[col] = features_df[col].astype(np.float32)
        
        # Store as contiguous arrays for better CPU cache performance
        self.features = np.ascontiguousarray(features_df.values, dtype=np.float32)
        self.targets = np.ascontiguousarray(df[target_column].values, dtype=np.int64)
        
        # Pre-allocate tensor for reuse (reduces memory allocation overhead)
        self._temp_sequence = np.empty((sequence_length, self.n_features), dtype=np.float32)
        
        # Memory usage reporting
        features_mb = self.features.nbytes / (1024 * 1024)
        targets_mb = self.targets.nbytes / (1024 * 1024)
        total_mb = features_mb + targets_mb
        
        print(f"💾 Memory usage:")
        print(f"   Features: {features_mb:.1f} MB")
        print(f"   Targets: {targets_mb:.1f} MB")
        print(f"   Total: {total_mb:.1f} MB")
        
        # Clean up temporary data
        del features_df
        gc.collect()

    def __len__(self):
        return len(self.features) - self.sequence_length + 1

    def __getitem__(self, idx):
        # Optimized sequence extraction
        end_idx = idx + self.sequence_length
        
        # Use pre-allocated array to avoid repeated allocations
        np.copyto(self._temp_sequence, self.features[idx:end_idx])
        
        # Convert to tensor efficiently
        sequence_tensor = torch.from_numpy(self._temp_sequence.copy())
        target_tensor = torch.tensor(self.targets[end_idx - 1], dtype=torch.long)
        
        return sequence_tensor, target_tensor
    
    def get_memory_usage_mb(self):
        """Get current memory usage in MB"""
        return (self.features.nbytes + self.targets.nbytes) / (1024 * 1024)


def create_optimized_dataloaders(train_dataset, val_dataset, test_dataset, batch_size=64):
    """
    Create CPU-optimized DataLoaders with optimal worker configuration
    """
    # Determine optimal number of workers
    cpu_count = psutil.cpu_count(logical=False)
    optimal_workers = min(cpu_count, 4)  # Cap at 4 to prevent resource contention
    use_pin_memory = torch.cuda.is_available()  # Only use pin_memory with GPU
    
    print(f"💻 DataLoader Optimization:")
    print(f"   Physical CPUs: {cpu_count}")
    print(f"   Optimal workers: {optimal_workers}")
    print(f"   Pin memory: {use_pin_memory}")
    print(f"   Batch size: {batch_size}")
    
    # Common DataLoader settings
    dataloader_kwargs = {
        'batch_size': batch_size,
        'num_workers': optimal_workers,
        'pin_memory': use_pin_memory,
        'persistent_workers': True if optimal_workers > 0 else False,
        'prefetch_factor': 2 if optimal_workers > 0 else 2
    }
    
    # Create optimized DataLoaders
    train_loader = DataLoader(train_dataset, shuffle=True, **dataloader_kwargs)
    val_loader = DataLoader(val_dataset, shuffle=False, **dataloader_kwargs)
    test_loader = DataLoader(test_dataset, shuffle=False, **dataloader_kwargs)
    
    return train_loader, val_loader, test_loader


def get_cpu_optimized_trainer_config(config):
    """
    Get CPU-optimized trainer configuration
    """
    is_gpu_available = torch.cuda.is_available()
    cpu_cores = psutil.cpu_count(logical=False)
    
    if not is_gpu_available:
        print("🖥️ Configuring for CPU-only training...")
        trainer_config = {
            'accelerator': 'cpu',
            'devices': 1,
            'precision': 32,  # Use 32-bit for CPU
            'accumulate_grad_batches': 4,  # Higher accumulation for CPU stability
            'enable_progress_bar': True,
            'enable_model_summary': True,
            'num_sanity_val_steps': 2,  # Reduce sanity checks
        }
        precision_info = "32-bit (CPU optimized)"
        accumulation_info = "4 batches (CPU optimized)"
    else:
        print("🚀 Configuring for GPU training...")
        trainer_config = {
            'accelerator': 'gpu',
            'devices': 1,
            'precision': 16,  # Mixed precision for GPU
            'accumulate_grad_batches': 2,
            'enable_progress_bar': True,
            'enable_model_summary': True,
        }
        precision_info = "16-bit mixed precision"
        accumulation_info = "2 batches"
    
    print(f"✅ Trainer configuration:")
    print(f"   Hardware: {'GPU' if is_gpu_available else 'CPU'}")
    print(f"   Precision: {precision_info}")
    print(f"   Gradient accumulation: {accumulation_info}")
    print(f"   CPU cores available: {cpu_cores}")
    
    return trainer_config


def monitor_system_resources():
    """
    Monitor current system resource usage
    """
    memory = psutil.virtual_memory()
    cpu_percent = psutil.cpu_percent(interval=1)
    
    print(f"🖥️ System Resources:")
    print(f"   CPU Usage: {cpu_percent:.1f}%")
    print(f"   Memory Usage: {memory.percent:.1f}%")
    print(f"   Available RAM: {memory.available / (1024**3):.1f} GB")
    print(f"   Used RAM: {memory.used / (1024**3):.1f} GB")
    
    return {
        'cpu_percent': cpu_percent,
        'memory_percent': memory.percent,
        'available_gb': memory.available / (1024**3),
        'used_gb': memory.used / (1024**3)
    }


def optimize_memory_usage():
    """
    Optimize memory usage and clean up
    """
    print("🧹 Optimizing memory usage...")
    
    # Force garbage collection
    gc.collect()
    
    # Clear PyTorch cache if using GPU
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    # Get memory info after cleanup
    memory = psutil.virtual_memory()
    print(f"💾 Memory after optimization:")
    print(f"   Available: {memory.available / (1024**3):.1f} GB")
    print(f"   Used: {memory.used / (1024**3):.1f} GB")
    print(f"   Usage: {memory.percent:.1f}%")


# Enhanced configuration for CPU optimization
def get_cpu_optimized_config(base_config):
    """
    Get enhanced configuration optimized for CPU training
    """
    cpu_cores = psutil.cpu_count(logical=False)
    available_ram = psutil.virtual_memory().total / (1024**3)
    
    # Adjust batch size based on available RAM
    if available_ram < 8:
        batch_size = 32  # Smaller batch for low RAM
    elif available_ram < 16:
        batch_size = 64  # Standard batch size
    else:
        batch_size = 128  # Larger batch for high RAM
    
    enhanced_config = base_config.copy()
    enhanced_config.update({
        # CPU-optimized settings
        'batch_size': batch_size,
        'dropout': 0.25,  # Balanced regularization
        'learning_rate': 3e-5,  # Conservative for stability
        'early_stopping_patience': 4,
        'lr_scheduler_patience': 2,
        'gradient_clip_val': 0.5,
        'max_epochs': 25,
        
        # CPU-specific optimizations
        'num_workers': min(cpu_cores, 4),
        'pin_memory': torch.cuda.is_available(),
        'persistent_workers': True,
    })
    
    print(f"🔧 CPU-Optimized Configuration:")
    print(f"   Batch size: {batch_size} (based on {available_ram:.1f}GB RAM)")
    print(f"   Workers: {enhanced_config['num_workers']}")
    print(f"   Learning rate: {enhanced_config['learning_rate']}")
    
    return enhanced_config


if __name__ == "__main__":
    print("🚀 CELEST AI CPU Optimization Module")
    print("=" * 50)
    
    # Setup CPU optimizations
    cpu_info = setup_cpu_optimization()
    
    # Monitor initial resources
    print("\n📊 Initial system resources:")
    initial_resources = monitor_system_resources()
    
    # Optimize memory
    optimize_memory_usage()
    
    print("\n✅ CPU optimization setup complete!")
    print("Add these optimizations to your notebook for better performance.")
