{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🌞 CELEST AI: PatchTST Training on Kaggle GPU\n", "\n", "This notebook trains the PatchTST transformer model for CME detection using <PERSON><PERSON>'s free GPU.\n", "\n", "## Setup Instructions:\n", "1. Upload your `training_data_2010_2011.parquet` file to Kaggle dataset\n", "2. Enable GPU in Kaggle notebook settings\n", "3. Run all cells\n", "\n", "## Expected Results:\n", "- F1 Score > 0.82 target\n", "- MLflow experiment tracking\n", "- Model artifacts for deployment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install pytorch-lightning mlflow shap plotly\n", "\n", "import pandas as pd\n", "import numpy as np\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "from torch.utils.data import Dataset, DataLoader\n", "import pytorch_lightning as pl\n", "from pytorch_lightning.callbacks import ModelCheckpoint, EarlyStopping\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score, f1_score\n", "import mlflow\n", "import mlflow.pytorch\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from typing import Dict, List, Tuple, Optional\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(f\"PyTorch version: {torch.__version__}\")\n", "print(f\"CUDA available: {torch.cuda.is_available()}\")\n", "print(f\"GPU device: {torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'CPU only'}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# REVISED CONFIGURATION FOR STABLE TRAINING\n", "CONFIG = {\n", "    'data_path': '/kaggle/input/hackthon/training_data_2010_2011.parquet',\n", "    'sequence_length': 180,  # 3 hours of 1-minute data\n", "    'patch_size': 12,       # 12-minute patches\n", "    'd_model': 128,         # Model dimension\n", "    'n_heads': 8,           # Number of attention heads\n", "    'n_layers': 6,          # Number of transformer layers\n", "    'dropout': 0.2,         # Increased from 0.1 to prevent overfitting\n", "    'learning_rate': 1e-5,  # Drastically reduced from 1e-4 for stability\n", "    'max_epochs': 30,       # Increased to allow for slower but stable learning\n", "    'batch_size': 64,       # Increased batch size for more stable gradients\n", "    'test_size': 0.2,\n", "    'val_size': 0.1,\n", "    'target_f1': 0.82,      # Target F1 score\n", "    \n", "    # Learning rate scheduler parameters\n", "    'lr_scheduler_patience': 3,    # Wait 3 epochs before reducing LR\n", "    'lr_scheduler_factor': 0.5,    # Reduce LR by half when plateau detected\n", "    'lr_scheduler_verbose': True   # Print messages when LR is reduced\n", "}\n", "\n", "print(\"Training Configuration:\")\n", "for key, value in CONFIG.items():\n", "    print(f\"  {key}: {value}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# CME Dataset Class\n", "class CMEDataset(Dataset):\n", "    def __init__(self, data: pd.DataFrame, sequence_length: int = 180, \n", "                 target_column: str = 'event_label', feature_columns: List[str] = None):\n", "        self.data = data.sort_values('timestamp').reset_index(drop=True)\n", "        self.sequence_length = sequence_length\n", "        self.target_column = target_column\n", "        \n", "        if feature_columns is None:\n", "            self.feature_columns = [\n", "                'Bz_gsm', 'B_total', 'speed', 'density', 'temperature',\n", "                'dynamic_pressure', 'clock_angle'\n", "            ]\n", "        else:\n", "            self.feature_columns = feature_columns\n", "        \n", "        # Filter available columns\n", "        self.feature_columns = [col for col in self.feature_columns if col in data.columns]\n", "        \n", "        # Prepare sequences\n", "        self.sequences, self.targets = self._create_sequences()\n", "        \n", "        print(f\"Created dataset with {len(self.sequences)} sequences\")\n", "        print(f\"Features: {self.feature_columns}\")\n", "    \n", "    def _create_sequences(self) -> Tuple[np.ndarray, np.ndarray]:\n", "        sequences = []\n", "        targets = []\n", "        \n", "        for i in range(len(self.data) - self.sequence_length):\n", "            seq_data = self.data.iloc[i:i+self.sequence_length][self.feature_columns].values\n", "            target = self.data.iloc[i+self.sequence_length][self.target_column]\n", "            \n", "            if not np.isnan(seq_data).any():\n", "                sequences.append(seq_data)\n", "                targets.append(target)\n", "        \n", "        return np.array(sequences), np.array(targets)\n", "    \n", "    def __len__(self):\n", "        return len(self.sequences)\n", "    \n", "    def __getitem__(self, idx):\n", "        return torch.FloatTensor(self.sequences[idx]), torch.LongTensor([self.targets[idx]])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# PatchTST Model Implementation\n", "class PatchTSTModel(pl.LightningModule):\n", "    def __init__(self, n_features: int, sequence_length: int, patch_size: int = 12,\n", "                 d_model: int = 128, n_heads: int = 8, n_layers: int = 6,\n", "                 dropout: float = 0.1, learning_rate: float = 1e-4,\n", "                 class_weights: Optional[torch.Tensor] = None):\n", "        super().__init__()\n", "        \n", "        self.save_hyperparameters()\n", "        \n", "        self.n_features = n_features\n", "        self.sequence_length = sequence_length\n", "        self.patch_size = patch_size\n", "        self.n_patches = sequence_length // patch_size\n", "        self.d_model = d_model\n", "        self.learning_rate = learning_rate\n", "        \n", "        # Patch embedding\n", "        self.patch_embedding = nn.Linear(patch_size * n_features, d_model)\n", "        \n", "        # Positional encoding\n", "        self.pos_encoding = nn.Parameter(torch.randn(1, self.n_patches, d_model))\n", "        \n", "        # Transformer encoder\n", "        encoder_layer = nn.TransformerEncoderLayer(\n", "            d_model=d_model, nhead=n_heads, dim_feedforward=d_model * 4,\n", "            dropout=dropout, batch_first=True\n", "        )\n", "        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=n_layers)\n", "        \n", "        # Classification head\n", "        self.classifier = nn.Sequential(\n", "            nn.<PERSON><PERSON><PERSON><PERSON>(d_model),\n", "            nn.Dropout(dropout),\n", "            nn.Linear(d_model, d_model // 2),\n", "            nn.ReLU(),\n", "            nn.Dropout(dropout),\n", "            nn.Linear(d_model // 2, 2)  # Binary classification\n", "        )\n", "        \n", "        # Loss function\n", "        if class_weights is not None:\n", "            self.criterion = nn.CrossEntropyLoss(weight=class_weights)\n", "        else:\n", "            self.criterion = nn.CrossEntropyLoss()\n", "        \n", "        # Metrics storage\n", "        self.validation_step_outputs = []\n", "    \n", "    def forward(self, x):\n", "        batch_size = x.size(0)\n", "        \n", "        # Reshape to patches\n", "        x = x.view(batch_size, self.n_patches, self.patch_size * self.n_features)\n", "        \n", "        # Patch embedding\n", "        x = self.patch_embedding(x)\n", "        \n", "        # Add positional encoding\n", "        x = x + self.pos_encoding\n", "        \n", "        # Transformer encoding\n", "        x = self.transformer(x)\n", "        \n", "        # Global average pooling\n", "        x = x.mean(dim=1)\n", "        \n", "        # Classification\n", "        logits = self.classifier(x)\n", "        \n", "        return logits\n", "    \n", "    def training_step(self, batch, batch_idx):\n", "        x, y = batch\n", "        y = y.squeeze()\n", "        \n", "        logits = self(x)\n", "        loss = self.criterion(logits, y)\n", "        \n", "        preds = torch.argmax(logits, dim=1)\n", "        acc = (preds == y).float().mean()\n", "        \n", "        self.log('train_loss', loss, on_step=True, on_epoch=True, prog_bar=True)\n", "        self.log('train_acc', acc, on_step=True, on_epoch=True, prog_bar=True)\n", "        \n", "        return loss\n", "    \n", "    def validation_step(self, batch, batch_idx):\n", "        x, y = batch\n", "        y = y.squeeze()\n", "        \n", "        logits = self(x)\n", "        loss = self.criterion(logits, y)\n", "        \n", "        preds = torch.argmax(logits, dim=1)\n", "        acc = (preds == y).float().mean()\n", "        probs = torch.softmax(logits, dim=1)[:, 1]\n", "        \n", "        self.log('val_loss', loss, on_epoch=True, prog_bar=True)\n", "        self.log('val_acc', acc, on_epoch=True, prog_bar=True)\n", "        \n", "        self.validation_step_outputs.append({\n", "            'preds': preds, 'targets': y, 'probs': probs\n", "        })\n", "        \n", "        return loss\n", "    \n", "    def on_validation_epoch_end(self):\n", "        if not self.validation_step_outputs:\n", "            return\n", "        \n", "        all_preds = torch.cat([x['preds'] for x in self.validation_step_outputs])\n", "        all_targets = torch.cat([x['targets'] for x in self.validation_step_outputs])\n", "        all_probs = torch.cat([x['probs'] for x in self.validation_step_outputs])\n", "        \n", "        # Calculate F1 score\n", "        f1 = f1_score(all_targets.cpu().numpy(), all_preds.cpu().numpy())\n", "        self.log('val_f1', f1, on_epoch=True, prog_bar=True)\n", "        \n", "        # Calculate AUC\n", "        try:\n", "            auc = roc_auc_score(all_targets.cpu().numpy(), all_probs.cpu().numpy())\n", "            self.log('val_auc', auc, on_epoch=True, prog_bar=True)\n", "        except ValueError:\n", "            pass\n", "        \n", "        self.validation_step_outputs.clear()\n", "    \n", "    def configure_optimizers(self):\n", "        \"\"\"\n", "        Configure optimizer and learning rate scheduler for stable training\n", "        \"\"\"\n", "        # Define the optimizer with improved settings\n", "        optimizer = optim.AdamW(\n", "            self.parameters(),\n", "            lr=self.learning_rate,\n", "            weight_decay=1e-5\n", "        )\n", "        \n", "        # Define the learning rate scheduler with improved parameters\n", "        # It will watch 'val_loss' and reduce the LR if it plateaus\n", "        scheduler = optim.lr_scheduler.ReduceLROnPlateau(\n", "            optimizer,\n", "            mode='min',                    # We want to minimize the validation loss\n", "            patience=3,                    # Wait 3 epochs with no improvement before reducing LR\n", "            factor=0.5,                    # Reduce LR by half (e.g., 1e-5 -> 5e-6)\n", "            verbose=True,                  # Print a message when the LR is reduced\n", "            min_lr=1e-7                    # Don't reduce below this value\n", "        )\n", "        \n", "        # Return the configuration for PyTorch Lightning\n", "        return {\n", "            'optimizer': optimizer,\n", "            'lr_scheduler': {\n", "                'scheduler': scheduler,\n", "                'monitor': 'val_loss',     # The metric to monitor\n", "                'interval': 'epoch',\n", "                'frequency': 1,\n", "            }\n", "        }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Data Loading and Preparation\n", "def prepare_data(data_path: str, config: dict):\n", "    print(f\"Loading data from {data_path}\")\n", "    \n", "    # Load data\n", "    data = pd.read_parquet(data_path)\n", "    print(f\"Loaded {len(data)} records\")\n", "    \n", "    # Display data info\n", "    print(\"\\nData Info:\")\n", "    print(f\"Date range: {data['timestamp'].min()} to {data['timestamp'].max()}\")\n", "    print(f\"Columns: {list(data.columns)}\")\n", "    \n", "    # Check target distribution\n", "    if 'event_label' in data.columns:\n", "        target_dist = data['event_label'].value_counts()\n", "        print(f\"\\nTarget distribution:\")\n", "        print(target_dist)\n", "        print(f\"Positive rate: {target_dist[1] / len(data):.3f}\")\n", "    \n", "    # Select features\n", "    feature_columns = [\n", "        'Bz_gsm', 'B_total', 'speed', 'density', 'temperature',\n", "        'dynamic_pressure', 'clock_angle'\n", "    ]\n", "    available_features = [col for col in feature_columns if col in data.columns]\n", "    print(f\"\\nUsing features: {available_features}\")\n", "    \n", "    # Add enhanced physics-informed features\n", "    print(\"\\n🔬 Adding enhanced physics-informed features...\")\n", "    data = add_enhanced_physics_features(data)\n", "    \n", "    # Update available features list\n", "    physics_features = [\n", "        'bz_southward_persistence', 'bz_intensity_30m', 'bz_to_btotal_ratio',\n", "        'speed_acceleration_10m', 'speed_enhancement', 'density_enhancement',\n", "        'dynamic_pressure', 'kinetic_to_magnetic_pressure', 'geoeffectiveness_proxy'\n", "    ]\n", "    available_features.extend([f for f in physics_features if f in data.columns])\n", "    print(f\"Enhanced features: {len(available_features)} total features\")\n", "    \n", "    # Handle missing values\n", "    data = data.dropna(subset=available_features + ['event_label'])\n", "    print(f\"After removing NaN: {len(data)} records\")\n", "    \n", "    # Temporal split (important for time series)\n", "    n_total = len(data)\n", "    n_test = int(n_total * config['test_size'])\n", "    n_val = int((n_total - n_test) * config['val_size'])\n", "    \n", "    train_data = data.iloc[:-n_test-n_val]\n", "    val_data = data.iloc[-n_test-n_val:-n_test]\n", "    test_data = data.iloc[-n_test:]\n", "    \n", "    print(f\"\\nData split: Train={len(train_data)}, Val={len(val_data)}, Test={len(test_data)}\")\n", "    \n", "    # Scale features\n", "    scaler = StandardScaler()\n", "    train_data[available_features] = scaler.fit_transform(train_data[available_features])\n", "    val_data[available_features] = scaler.transform(val_data[available_features])\n", "    test_data[available_features] = scaler.transform(test_data[available_features])\n", "    \n", "    return train_data, val_data, test_data, scaler, available_features\n", "\n", "# Load and prepare data\n", "train_data, val_data, test_data, scaler, feature_columns = prepare_data(CONFIG['data_path'], CONFIG)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create datasets and data loaders\n", "print(\"Creating datasets...\")\n", "\n", "train_dataset = CMEDataset(train_data, CONFIG['sequence_length'], feature_columns=feature_columns)\n", "val_dataset = CMEDataset(val_data, CONFIG['sequence_length'], feature_columns=feature_columns)\n", "test_dataset = CMEDataset(test_data, CONFIG['sequence_length'], feature_columns=feature_columns)\n", "\n", "# Create data loaders\n", "train_loader = DataLoader(train_dataset, batch_size=CONFIG['batch_size'], shuffle=True)\n", "val_loader = DataLoader(val_dataset, batch_size=CONFIG['batch_size'], shuffle=False)\n", "test_loader = DataLoader(test_dataset, batch_size=CONFIG['batch_size'], shuffle=False)\n", "\n", "print(f\"Train batches: {len(train_loader)}\")\n", "print(f\"Val batches: {len(val_loader)}\")\n", "print(f\"Test batches: {len(test_loader)}\")\n", "\n", "# Calculate class weights for imbalanced data\n", "train_targets = []\n", "for batch in train_loader:\n", "    _, targets = batch\n", "    train_targets.extend(targets.squeeze().tolist())\n", "\n", "class_counts = np.bincount(train_targets)\n", "class_weights = torch.FloatTensor(len(class_counts) / class_counts)\n", "\n", "print(f\"\\nClass distribution: {class_counts}\")\n", "print(f\"Class weights: {class_weights}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize MLflow experiment\n", "mlflow.set_experiment(\"celest-ai-kaggle-training\")\n", "\n", "with mlflow.start_run(run_name=\"patchtst-synthetic-data\"):\n", "    # Log configuration\n", "    mlflow.log_params(CONFIG)\n", "    mlflow.log_param(\"n_features\", len(feature_columns))\n", "    mlflow.log_param(\"train_samples\", len(train_dataset))\n", "    mlflow.log_param(\"val_samples\", len(val_dataset))\n", "    mlflow.log_param(\"test_samples\", len(test_dataset))\n", "    \n", "    # Initialize model\n", "    model = PatchTSTModel(\n", "        n_features=len(feature_columns),\n", "        sequence_length=CONFIG['sequence_length'],\n", "        patch_size=CONFIG['patch_size'],\n", "        d_model=CONFIG['d_model'],\n", "        n_heads=CONFIG['n_heads'],\n", "        n_layers=CONFIG['n_layers'],\n", "        dropout=CONFIG['dropout'],\n", "        learning_rate=CONFIG['learning_rate'],\n", "        class_weights=class_weights\n", "    )\n", "    \n", "    print(f\"\\nModel initialized with {sum(p.numel() for p in model.parameters())} parameters\")\n", "    \n", "    # Setup callbacks\n", "    checkpoint_callback = ModelCheckpoint(\n", "        monitor='val_f1',\n", "        dirpath='./checkpoints',\n", "        filename='patchtst-{epoch:02d}-{val_f1:.3f}',\n", "        save_top_k=3,\n", "        mode='max'  # Maximize F1 score\n", "    )\n", "    \n", "    early_stopping = EarlyStopping(\n", "        monitor='val_f1',\n", "        patience=8,\n", "        mode='max'\n", "    )\n", "    \n", "    # Setup trainer with improved stability settings\n", "    trainer = pl.Trainer(\n", "        max_epochs=CONFIG['max_epochs'],\n", "        callbacks=[checkpoint_callback, early_stopping],\n", "        accelerator='gpu' if torch.cuda.is_available() else 'cpu',\n", "        devices=1,\n", "        log_every_n_steps=50,\n", "        precision=16 if torch.cuda.is_available() else 32,  # Mixed precision for GPU\n", "        gradient_clip_val=1.0,          # Prevent gradient explosion\n", "        deterministic=True              # For reproducible results\n", "    )\n", "    \n", "    # Add progress monitoring\n", "    print(\"\\n🚀 Starting improved training with:\")\n", "    print(f\"   📉 Learning Rate: {CONFIG['learning_rate']}\")\n", "    print(f\"   🎯 Target F1 Score: {CONFIG['target_f1']}\")\n", "    print(f\"   📊 Batch Size: {CONFIG['batch_size']}\")\n", "    print(f\"   🔄 Max Epochs: {CONFIG['max_epochs']}\")\n", "    print(f\"   🛡️ Dropout: {CONFIG['dropout']}\")\n", "    print(\"\\n⏳ Training in progress... Watch for:\")\n", "    print(\"   ✅ F1 scores should start improving from Epoch 2-3\")\n", "    print(\"   📈 Look for 'ReduceLROnPlateau reducing learning rate' messages\")\n", "    print(\"   🎯 Target: F1 Score ≥ 0.82\")\n", "    print(\"\\n\" + \"=\"*60)\n", "    \n", "    # Train model\n", "    trainer.fit(model, train_loader, val_loader)\n", "    \n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"✅ Training completed!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Model Evaluation and Results\n", "print(\"\\n📊 Evaluating model on test set...\")\n", "\n", "# Test the model\n", "trainer.test(model, test_loader)\n", "\n", "# Get predictions for detailed analysis\n", "model.eval()\n", "all_preds = []\n", "all_targets = []\n", "all_probs = []\n", "\n", "with torch.no_grad():\n", "    for batch in test_loader:\n", "        x, y = batch\n", "        if torch.cuda.is_available():\n", "            x, y = x.cuda(), y.cuda()\n", "        \n", "        logits = model(x)\n", "        preds = torch.argmax(logits, dim=1)\n", "        probs = torch.softmax(logits, dim=1)[:, 1]\n", "        \n", "        all_preds.extend(preds.cpu().numpy())\n", "        all_targets.extend(y.squeeze().cpu().numpy())\n", "        all_probs.extend(probs.cpu().numpy())\n", "\n", "# Calculate final metrics\n", "final_f1 = f1_score(all_targets, all_preds)\n", "final_auc = roc_auc_score(all_targets, all_probs)\n", "\n", "print(f\"\\n🎯 FINAL RESULTS:\")\n", "print(f\"F1 Score: {final_f1:.4f} (Target: {CONFIG['target_f1']})\")\n", "print(f\"AUC Score: {final_auc:.4f}\")\n", "print(f\"Target Achieved: {'✅ YES' if final_f1 >= CONFIG['target_f1'] else '❌ NO'}\")\n", "\n", "# Log final metrics to MLflow\n", "mlflow.log_metric(\"final_f1_score\", final_f1)\n", "mlflow.log_metric(\"final_auc_score\", final_auc)\n", "mlflow.log_metric(\"target_achieved\", 1 if final_f1 >= CONFIG['target_f1'] else 0)\n", "\n", "# Classification report\n", "print(\"\\n📋 Classification Report:\")\n", "print(classification_report(all_targets, all_preds, target_names=['Normal', 'CME Event']))\n", "\n", "# Confusion Matrix\n", "cm = confusion_matrix(all_targets, all_preds)\n", "plt.figure(figsize=(8, 6))\n", "sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', \n", "            xticklabels=['Normal', 'CME Event'], \n", "            yticklabels=['Normal', 'CME Event'])\n", "plt.title('Confusion Matrix - CELEST AI PatchTST Model')\n", "plt.ylabel('True Label')\n", "plt.xlabel('Predicted Label')\n", "plt.show()\n", "\n", "# Save model artifacts\n", "mlflow.pytorch.log_model(model, \"patchtst_model\")\n", "print(\"\\n💾 Model saved to MLflow\")\n", "\n", "print(\"\\n🌟 Training and evaluation completed successfully!\")\n", "print(f\"\\n📈 Summary:\")\n", "print(f\"   • Model: PatchTST Transformer\")\n", "print(f\"   • Data: Synthetic CME detection dataset\")\n", "print(f\"   • F1 Score: {final_f1:.4f}\")\n", "print(f\"   • AUC Score: {final_auc:.4f}\")\n", "print(f\"   • Target F1 > 0.82: {'✅ Achieved' if final_f1 >= 0.82 else '❌ Not achieved'}\")\n", "print(f\"\\n🚀 Ready for real data integration!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Next Steps\n", "\n", "### If F1 Score ≥ 0.82:\n", "✅ **Success!** The model meets the performance target.\n", "- Download the trained model from MLflow\n", "- Integrate with FastAPI service\n", "- Deploy to production environment\n", "- Replace synthetic data with real Aditya-L1 data\n", "\n", "### If F1 Score < 0.82:\n", "🔧 **Optimization needed:**\n", "- Increase model complexity (more layers/heads)\n", "- Adjust hyperparameters\n", "- Try different patch sizes\n", "- Implement ensemble methods\n", "- Add more sophisticated data augmentation\n", "\n", "### Model Deployment:\n", "1. Export model to ONNX format for faster inference\n", "2. Integrate with existing FastAPI service\n", "3. Add SHAP explainability\n", "4. Set up monitoring and alerting\n", "5. Connect to real-time data streams\n", "\n", "### Real Data Integration:\n", "- Replace synthetic data with actual OMNIWeb/Aditya-L1 data\n", "- Retrain model on real historical events\n", "- Validate against known CME events\n", "- Fine-tune PDCE physics parameters\n", "\n", "**🌞 CELEST AI is ready for operational deployment!**"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}