# ==============================================================================
# 🚀 CELEST AI - Enhanced Kaggle Implementation
# Complete physics-driven implementation for F1 > 0.82
# 
# INSTRUCTIONS:
# 1. Copy this entire code into a new Kaggle notebook cell
# 2. Place it BEFORE your existing data preparation
# 3. Update your prepare_data function call to use the enhanced version
# ==============================================================================

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import pytorch_lightning as pl
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import f1_score, classification_report
import warnings
warnings.filterwarnings('ignore')

def add_enhanced_physics_features(df):
    """
    Engineers a comprehensive set of physics-informed features with proper units
    and advanced solar wind physics calculations.
    """
    print('🔬 Engineering advanced physics-informed features...')
    
    # Create a copy to avoid modifying the original
    df = df.copy()
    
    # --- Core Magnetic Field Features ---
    df['bz_southward_persistence'] = (df['Bz_gsm'] < 0).rolling(window=30, min_periods=1).mean()
    df['bz_intensity_30m'] = df['Bz_gsm'].rolling(window=30, min_periods=1).min()
    df['bz_to_btotal_ratio'] = df['Bz_gsm'] / (df['B_total'] + 1e-6)
    df['bz_volatility'] = df['Bz_gsm'].rolling(window=10, min_periods=1).std()
    
    # Magnetic field enhancement and coherence
    df['btotal_enhancement'] = df['B_total'] / df['B_total'].rolling(window=60, min_periods=1).mean()
    df['magnetic_coherence'] = df['B_total'].rolling(window=30, min_periods=1).std() / df['B_total'].rolling(window=30, min_periods=1).mean()
    
    # --- Solar Wind Speed Features ---
    df['speed_acceleration_10m'] = df['speed'].diff(periods=10)
    df['speed_enhancement'] = df['speed'] / df['speed'].rolling(window=60, min_periods=1).mean()
    df['speed_volatility'] = df['speed'].rolling(window=15, min_periods=1).std()
    df['speed_shock_strength'] = df['speed'].rolling(window=5, min_periods=1).max() / df['speed'].rolling(window=60, min_periods=1).mean()
    
    # --- Plasma Density Features ---
    df['density_enhancement'] = df['density'] / df['density'].rolling(window=60, min_periods=1).mean()
    df['density_spike'] = df['density'].rolling(window=10, min_periods=1).max() / df['density'].rolling(window=60, min_periods=1).mean()
    df['density_compression'] = df['density'] / df['density'].shift(10)
    
    # --- Temperature Features ---
    df['temperature_depression'] = df['temperature'] / df['temperature'].rolling(window=60, min_periods=1).mean()
    expected_temp = (df['speed'] / 4.0) ** 2  # Empirical relation: T ~ V^2
    df['temperature_anomaly'] = df['temperature'] / (expected_temp + 1e6)
    
    # --- Advanced Pressure Calculations ---
    if 'dynamic_pressure' not in df.columns:
        df['dynamic_pressure'] = df['density'] * df['speed']**2 * 1.67e-6  # nPa
    
    # Magnetic pressure (proper units: nPa)
    df['magnetic_pressure'] = df['B_total']**2 / (2 * 4 * np.pi * 1e-7) * 1e9
    
    # Thermal pressure (proper units: nPa)
    df['thermal_pressure'] = df['density'] * 1.38e-23 * df['temperature'] * 1e9
    
    # Pressure ratios
    df['kinetic_to_magnetic_pressure'] = df['dynamic_pressure'] / (df['magnetic_pressure'] + 1e-6)
    df['plasma_beta'] = df['thermal_pressure'] / (df['magnetic_pressure'] + 1e-6)
    
    # --- Advanced Physics Parameters ---
    # Alfvén speed (proper calculation in km/s)
    mu0 = 4 * np.pi * 1e-7  # Permeability of free space
    mp = 1.67e-27  # Proton mass in kg
    df['alfven_speed'] = (df['B_total'] * 1e-9) / np.sqrt(mu0 * df['density'] * 1e6 * mp) / 1000
    df['alfven_mach'] = df['speed'] / (df['alfven_speed'] + 1e-6)
    
    # --- Geoeffectiveness Indicators ---
    # Electric field proxy (VBs in mV/m)
    df['electric_field_proxy'] = df['speed'] * np.abs(df['Bz_gsm']) * (df['Bz_gsm'] < 0) * 1e-3
    
    # Combined geoeffectiveness
    df['geoeffectiveness_proxy'] = df['speed'] * np.abs(df['Bz_gsm']) * (df['Bz_gsm'] < 0)
    
    # --- Interaction Terms ---
    df['bz_speed_interaction'] = np.abs(df['Bz_gsm']) * df['speed'] * (df['Bz_gsm'] < 0)
    df['bz_density_interaction'] = np.abs(df['Bz_gsm']) * df['density'] * (df['Bz_gsm'] < 0)
    df['speed_density_interaction'] = df['speed'] * df['density']
    
    # Fill NaNs
    df.fillna(method='bfill', inplace=True)
    df.fillna(method='ffill', inplace=True)
    
    # Count new features
    original_features = ['Bz_gsm', 'B_total', 'speed', 'density', 'temperature', 'clock_angle', 'timestamp', 'event_label', 'dynamic_pressure']
    new_features = [col for col in df.columns if col not in original_features]
    
    print(f'✅ Added {len(new_features)} advanced physics-informed features')
    return df


def physics_driven_labeling(df, look_ahead=45):
    """
    Enhanced physics-driven labeling with multi-criteria consensus.
    """
    print('🎯 Applying enhanced physics-driven labeling...')
    
    df = df.copy()
    
    # Define thresholds based on solar physics
    BZ_THRESHOLD = -7.5  # Very strong southward Bz (nT)
    SPEED_JUMP_THRESHOLD = 75  # Significant speed increase (km/s)
    DENSITY_THRESHOLD = 20.0   # High density (n/cm^3)
    BTOTAL_THRESHOLD = 15.0    # Strong magnetic field (nT)
    
    # Look ahead in time to find the peak of a potential event
    df['future_bz_peak'] = df['Bz_gsm'].rolling(window=look_ahead, min_periods=1).min()
    df['future_speed_jump'] = df['speed'].diff().rolling(window=look_ahead, min_periods=1).max()
    df['future_density_peak'] = df['density'].rolling(window=look_ahead, min_periods=1).max()
    df['future_btotal_peak'] = df['B_total'].rolling(window=look_ahead, min_periods=1).max()
    
    # Additional physics-based conditions
    df['future_dynamic_pressure'] = (df['density'] * df['speed']**2).rolling(window=look_ahead, min_periods=1).max()
    df['future_southward_duration'] = (df['Bz_gsm'] < -5).rolling(window=look_ahead, min_periods=1).sum()
    
    # Define the consensus condition for a high-confidence event
    primary_condition = df['future_bz_peak'] < BZ_THRESHOLD
    
    # Secondary conditions (at least 2 must be met)
    speed_condition = df['future_speed_jump'] > SPEED_JUMP_THRESHOLD
    density_condition = df['future_density_peak'] > DENSITY_THRESHOLD
    btotal_condition = df['future_btotal_peak'] > BTOTAL_THRESHOLD
    duration_condition = df['future_southward_duration'] >= 10
    pressure_condition = df['future_dynamic_pressure'] > df['future_dynamic_pressure'].quantile(0.9)
    
    # Count secondary conditions
    secondary_count = (
        speed_condition.astype(int) + 
        density_condition.astype(int) + 
        btotal_condition.astype(int) + 
        duration_condition.astype(int) + 
        pressure_condition.astype(int)
    )
    
    # Final consensus: Primary condition AND at least 2 secondary conditions
    is_cme_imminent = primary_condition & (secondary_count >= 2)
    
    # Shift the labels back so the model has a warning window
    df['event_label'] = is_cme_imminent.shift(-look_ahead).fillna(0).astype(int)
    
    # Post-processing: Remove isolated events and add persistence
    df['event_label_smooth'] = df['event_label'].rolling(window=5, min_periods=1, center=True).mean()
    df['event_label'] = (df['event_label_smooth'] > 0.4).astype(int)
    
    # Clean up temporary columns
    temp_columns = ['future_bz_peak', 'future_speed_jump', 'future_density_peak', 
                   'future_btotal_peak', 'future_dynamic_pressure', 'future_southward_duration',
                   'event_label_smooth']
    df.drop(columns=temp_columns, inplace=True)
    
    positive_count = df['event_label'].sum()
    positive_rate = df['event_label'].mean()
    
    print(f'✅ Generated {positive_count} positive labels ({positive_rate:.2%} of total)')
    return df


class PhysicsInformedLoss(nn.Module):
    """
    Physics-informed loss function for CME detection
    Combines focal loss for hard example mining with class balancing
    """
    def __init__(self, class_weights=None, alpha=0.25, gamma=2.0):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.class_weights = class_weights
    
    def forward(self, inputs, targets):
        # Focal loss for hard example mining
        ce_loss = F.cross_entropy(inputs, targets, weight=self.class_weights, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss
        return focal_loss.mean()


def prepare_data_enhanced(data_path: str, config: dict):
    """
    Enhanced data preparation with physics-driven labeling and feature engineering
    """
    print(f"📊 Loading data from {data_path}")
    
    # Load data
    data = pd.read_parquet(data_path)
    if 'timestamp' in data.columns:
        data['timestamp'] = pd.to_datetime(data['timestamp'])
        data = data.sort_values('timestamp').reset_index(drop=True)
    print(f"📊 Loaded {len(data)} records")
    
    # --- 1. Physics-Driven Labeling (CORE LOGIC) ---
    data = physics_driven_labeling(data, look_ahead=45)
    
    # Check target distribution
    target_dist = data['event_label'].value_counts()
    print(f"\n🎯 Target distribution post-PDCE:")
    print(target_dist)
    if 1 in target_dist:
        print(f"✅ Positive rate: {target_dist[1] / len(data):.3%}")
    else:
        print("❌ Warning: No positive labels generated.")
        return None, None, None, None, None

    # --- 2. Feature Engineering (CORE LOGIC) ---
    print("\n🔬 Adding enhanced physics-informed features...")
    data = add_enhanced_physics_features(data)

    # Define enhanced feature set
    base_features = ['Bz_gsm', 'B_total', 'speed', 'density', 'temperature', 'clock_angle']
    physics_features = [
        'bz_southward_persistence', 'bz_intensity_30m', 'bz_to_btotal_ratio', 'bz_volatility',
        'btotal_enhancement', 'magnetic_coherence', 'speed_acceleration_10m', 'speed_enhancement',
        'speed_volatility', 'speed_shock_strength', 'density_enhancement', 'density_spike',
        'density_compression', 'temperature_depression', 'temperature_anomaly', 'dynamic_pressure',
        'magnetic_pressure', 'thermal_pressure', 'kinetic_to_magnetic_pressure', 'plasma_beta',
        'alfven_speed', 'alfven_mach', 'electric_field_proxy', 'geoeffectiveness_proxy',
        'bz_speed_interaction', 'bz_density_interaction', 'speed_density_interaction'
    ]
    
    feature_columns = base_features + physics_features
    available_features = [col for col in feature_columns if col in data.columns]
    print(f"📊 Total features used: {len(available_features)}")
    
    # Handle missing values
    data = data.dropna(subset=available_features + ['event_label'])
    print(f"📊 After removing NaN: {len(data)} records")

    # --- 3. Data Splitting and Scaling ---
    n_total = len(data)
    n_test = int(n_total * config['test_size'])
    n_val = int((n_total - n_test) * config['val_size'])
    
    train_data = data.iloc[:-n_test-n_val]
    val_data = data.iloc[-n_test-n_val:-n_test]
    test_data = data.iloc[-n_test:]
    
    print(f"\n📊 Data split: Train={len(train_data)}, Val={len(val_data)}, Test={len(test_data)}")
    
    # Scale features
    scaler = StandardScaler()
    train_data_scaled = train_data.copy()
    val_data_scaled = val_data.copy()
    test_data_scaled = test_data.copy()
    
    train_data_scaled[available_features] = scaler.fit_transform(train_data[available_features])
    val_data_scaled[available_features] = scaler.transform(val_data[available_features])
    test_data_scaled[available_features] = scaler.transform(test_data[available_features])
    
    return train_data_scaled, val_data_scaled, test_data_scaled, scaler, available_features

print("🚀 Enhanced CELEST AI Core Logic Ready!")
print("=" * 50)
print("✅ Advanced physics features")
print("✅ Enhanced consensus labeling") 
print("✅ Physics-informed loss function")
print("✅ Complete data pipeline")
print("=" * 50)
