#!/usr/bin/env python3
"""
CELEST AI - Run Complete Fix

This script executes the complete fix for your CELEST AI production notebook.
Simply run this in your notebook to fix all issues.

Usage:
    exec(open('notebooks/run_complete_fix.py').read())
"""

print("🚀 CELEST AI Complete Fix Execution")
print("=" * 50)

# Load the complete fix
try:
    exec(open('notebooks/celest_ai_complete_fix.py').read())
    print("✅ Complete fix loaded successfully")
except Exception as e:
    print(f"❌ Error loading complete fix: {e}")
    exit()

# Check if required variables exist
required_vars = ['enhanced_df']
missing_vars = []

for var in required_vars:
    if var not in globals():
        missing_vars.append(var)

if missing_vars:
    print(f"❌ Missing required variables: {missing_vars}")
    print("Please ensure you have run the data loading and processing steps first.")
    exit()

print("\n🔧 Applying Complete Fix to CELEST AI...")

try:
    # Apply the complete fix
    df_fixed, model_fixed, production_config, class_weights = apply_complete_celest_fix(
        enhanced_df, 
        model if 'model' in globals() else None,
        threshold_level='lenient'
    )
    
    print("\n✅ Complete fix applied successfully!")
    
    # Validate the fix
    event_rate = df_fixed['event_label'].mean() * 100
    print(f"\n📊 Fix Validation:")
    print(f"   Event rate: {event_rate:.2f}%")
    
    if event_rate < 5.0:
        print("   ⚠️ Event rate still low, trying adaptive thresholds...")
        df_fixed, model_fixed, production_config, class_weights = apply_complete_celest_fix(
            enhanced_df, 
            model if 'model' in globals() else None,
            threshold_level='adaptive'
        )
        event_rate = df_fixed['event_label'].mean() * 100
        print(f"   New event rate: {event_rate:.2f}%")
    
    if event_rate >= 5.0:
        print("   ✅ Event rate is now suitable for training")
    else:
        print("   ⚠️ Event rate still low - may need manual threshold adjustment")
    
    # Create new datasets with fixed data
    print("\n📊 Creating new datasets with fixed data...")
    
    # Split the fixed data
    split_idx_1 = int(0.7 * len(df_fixed))
    split_idx_2 = int(0.85 * len(df_fixed))
    
    train_df_fixed = df_fixed.iloc[:split_idx_1].copy()
    val_df_fixed = df_fixed.iloc[split_idx_1:split_idx_2].copy()
    test_df_fixed = df_fixed.iloc[split_idx_2:].copy()
    
    print(f"   Train: {len(train_df_fixed):,} samples")
    print(f"   Validation: {len(val_df_fixed):,} samples") 
    print(f"   Test: {len(test_df_fixed):,} samples")
    
    # Check class distribution in each split
    for name, df_split in [('Train', train_df_fixed), ('Val', val_df_fixed), ('Test', test_df_fixed)]:
        split_event_rate = df_split['event_label'].mean() * 100
        print(f"   {name} event rate: {split_event_rate:.2f}%")
    
    # Update global variables for use in notebook
    globals()['enhanced_df_fixed'] = df_fixed
    globals()['train_df_fixed'] = train_df_fixed
    globals()['val_df_fixed'] = val_df_fixed
    globals()['test_df_fixed'] = test_df_fixed
    globals()['production_config'] = production_config
    globals()['production_class_weights'] = class_weights
    
    if model_fixed is not None:
        globals()['model_fixed'] = model_fixed
    
    print("\n🎯 Ready for Production Training!")
    print("   Variables created:")
    print("   - enhanced_df_fixed: Fixed dataset")
    print("   - train_df_fixed, val_df_fixed, test_df_fixed: Fixed splits")
    print("   - production_config: Optimized training config")
    print("   - production_class_weights: Balanced class weights")
    if model_fixed is not None:
        print("   - model_fixed: Model with test methods added")
    
    print("\n📋 Next Steps:")
    print("   1. Use the fixed datasets for training")
    print("   2. Apply the production_config settings")
    print("   3. Use production_class_weights in your loss function")
    print("   4. Train with smaller learning rate and more epochs")
    
    # Show example usage
    print("\n💡 Example Usage:")
    print("""
    # Create new datasets with fixed data
    train_dataset_fixed = ProductionCMEDataset(train_df_fixed, feature_columns, 'event_label', 180)
    val_dataset_fixed = ProductionCMEDataset(val_df_fixed, feature_columns, 'event_label', 180)
    test_dataset_fixed = ProductionCMEDataset(test_df_fixed, feature_columns, 'event_label', 180)
    
    # Create new dataloaders
    train_loader_fixed = DataLoader(train_dataset_fixed, batch_size=production_config['batch_size'], shuffle=True)
    val_loader_fixed = DataLoader(val_dataset_fixed, batch_size=production_config['batch_size'])
    test_loader_fixed = DataLoader(test_dataset_fixed, batch_size=production_config['batch_size'])
    
    # Create new model with fixed loss function
    model_new = ProductionPatchTSTModel(production_config, len(feature_columns), production_class_weights)
    
    # Train with production settings
    trainer = pl.Trainer(
        max_epochs=production_config['max_epochs'],
        gradient_clip_val=production_config['gradient_clip_val'],
        # ... other settings
    )
    
    trainer.fit(model_new, train_loader_fixed, val_loader_fixed)
    """)

except Exception as e:
    print(f"❌ Error applying complete fix: {e}")
    import traceback
    traceback.print_exc()

print("\n🎉 Complete Fix Execution Finished!")
