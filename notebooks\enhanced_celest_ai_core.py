# ==============================================================================
# 🔬 ENHANCED CELEST AI: Advanced Physics-Driven Feature Engineering and Labeling
# This is an optimized version of your enhanced implementation with additional
# physics corrections and performance improvements.
# ==============================================================================

import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, confusion_matrix, f1_score
import warnings
warnings.filterwarnings('ignore')

def add_enhanced_physics_features(df: pd.DataFrame) -> pd.DataFrame:
    """
    Engineers a comprehensive set of physics-informed features with proper units
    and advanced solar wind physics calculations.
    """
    print("🔬 Engineering advanced physics-informed features...")
    
    # Create a copy to avoid modifying the original
    df = df.copy()
    
    # --- Core Magnetic Field Features ---
    df['bz_southward_persistence'] = (df['Bz_gsm'] < 0).rolling(window=30, min_periods=1).mean()
    df['bz_intensity_30m'] = df['Bz_gsm'].rolling(window=30, min_periods=1).min()
    df['bz_to_btotal_ratio'] = df['Bz_gsm'] / (df['B_total'] + 1e-6)
    df['bz_volatility'] = df['Bz_gsm'].rolling(window=10, min_periods=1).std()
    
    # Magnetic field enhancement and coherence
    df['btotal_enhancement'] = df['B_total'] / df['B_total'].rolling(window=60, min_periods=1).mean()
    df['magnetic_coherence'] = df['B_total'].rolling(window=30, min_periods=1).std() / df['B_total'].rolling(window=30, min_periods=1).mean()
    
    # --- Solar Wind Speed Features ---
    df['speed_acceleration_10m'] = df['speed'].diff(periods=10)
    df['speed_enhancement'] = df['speed'] / df['speed'].rolling(window=60, min_periods=1).mean()
    df['speed_volatility'] = df['speed'].rolling(window=15, min_periods=1).std()
    
    # Speed shock indicators
    df['speed_shock_strength'] = df['speed'].rolling(window=5, min_periods=1).max() / df['speed'].rolling(window=60, min_periods=1).mean()
    
    # --- Plasma Density Features ---
    df['density_enhancement'] = df['density'] / df['density'].rolling(window=60, min_periods=1).mean()
    df['density_spike'] = df['density'].rolling(window=10, min_periods=1).max() / df['density'].rolling(window=60, min_periods=1).mean()
    df['density_compression'] = df['density'] / df['density'].shift(10)
    
    # --- Temperature Features ---
    df['temperature_depression'] = df['temperature'] / df['temperature'].rolling(window=60, min_periods=1).mean()
    
    # Expected temperature from speed (empirical solar wind relation)
    expected_temp = (df['speed'] / 4.0) ** 2  # Empirical relation: T ~ V^2
    df['temperature_anomaly'] = df['temperature'] / (expected_temp + 1e6)
    
    # --- Advanced Pressure Calculations ---
    # Dynamic pressure (proper units: nPa)
    if 'dynamic_pressure' not in df.columns:
        df['dynamic_pressure'] = df['density'] * df['speed']**2 * 1.67e-6  # nPa
    
    # Magnetic pressure (proper units: nPa)
    df['magnetic_pressure'] = df['B_total']**2 / (2 * 4 * np.pi * 1e-7) * 1e9  # nPa
    
    # Thermal pressure (proper units: nPa)
    df['thermal_pressure'] = df['density'] * 1.38e-23 * df['temperature'] * 1e9  # nPa
    
    # Pressure ratios
    df['kinetic_to_magnetic_pressure'] = df['dynamic_pressure'] / (df['magnetic_pressure'] + 1e-6)
    df['plasma_beta'] = df['thermal_pressure'] / (df['magnetic_pressure'] + 1e-6)
    df['total_pressure'] = df['dynamic_pressure'] + df['magnetic_pressure'] + df['thermal_pressure']
    
    # --- Advanced Physics Parameters ---
    # Alfvén speed (proper calculation in km/s)
    mu0 = 4 * np.pi * 1e-7  # Permeability of free space
    mp = 1.67e-27  # Proton mass in kg
    df['alfven_speed'] = (df['B_total'] * 1e-9) / np.sqrt(mu0 * df['density'] * 1e6 * mp) / 1000  # km/s
    
    # Mach numbers
    df['alfven_mach'] = df['speed'] / (df['alfven_speed'] + 1e-6)
    
    # Sound speed (km/s)
    gamma = 5/3  # Adiabatic index for monatomic gas
    kb = 1.38e-23  # Boltzmann constant
    sound_speed = np.sqrt(gamma * kb * df['temperature'] / mp) / 1000  # km/s
    df['sound_speed'] = sound_speed
    df['sonic_mach'] = df['speed'] / (sound_speed + 1e-6)
    
    # Magnetosonic speed and Mach number
    magnetosonic_speed = np.sqrt(df['alfven_speed']**2 + sound_speed**2)
    df['magnetosonic_mach'] = df['speed'] / (magnetosonic_speed + 1e-6)
    
    # --- Clock Angle Features ---
    if 'clock_angle' in df.columns:
        df['clock_angle_sin'] = np.sin(np.radians(df['clock_angle']))
        df['clock_angle_cos'] = np.cos(np.radians(df['clock_angle']))
        df['clock_angle_geoeff'] = np.sin(np.radians(df['clock_angle'] / 2))**4  # Geoeffectiveness function
    
    # --- Geoeffectiveness Indicators ---
    # Electric field proxy (VBs in mV/m)
    df['electric_field_proxy'] = df['speed'] * np.abs(df['Bz_gsm']) * (df['Bz_gsm'] < 0) * 1e-3
    
    # Combined geoeffectiveness (Newell coupling function)
    df['newell_coupling'] = (df['speed']**(4/3)) * (df['B_total']**(2/3)) * (np.sin(np.radians(df.get('clock_angle', 0)/2))**4 if 'clock_angle' in df.columns else 1)
    
    # Burton et al. geoeffectiveness
    df['burton_geoeff'] = df['speed'] * df['B_total'] * np.sin(np.radians(df.get('clock_angle', 0)/2))**2 if 'clock_angle' in df.columns else df['speed'] * np.abs(df['Bz_gsm']) * (df['Bz_gsm'] < 0)
    
    # --- Gradient and Variability Features ---
    for param in ['Bz_gsm', 'speed', 'density', 'B_total']:
        df[f'{param}_gradient'] = df[param].diff()
        df[f'{param}_gradient_abs'] = df[param].diff().abs()
        df[f'{param}_acceleration'] = df[param].diff().diff()
    
    # --- Multi-timescale Statistical Features ---
    windows = [5, 15, 30, 60, 120]
    params = ['Bz_gsm', 'speed', 'B_total', 'density']
    
    for window in windows:
        for param in params:
            if param in df.columns:
                df[f'{param}_mean_{window}m'] = df[param].rolling(window=window, min_periods=1).mean()
                df[f'{param}_std_{window}m'] = df[param].rolling(window=window, min_periods=1).std()
                df[f'{param}_min_{window}m'] = df[param].rolling(window=window, min_periods=1).min()
                df[f'{param}_max_{window}m'] = df[param].rolling(window=window, min_periods=1).max()
                df[f'{param}_range_{window}m'] = df[f'{param}_max_{window}m'] - df[f'{param}_min_{window}m']
    
    # --- Interaction Terms ---
    # Critical for CME identification
    df['bz_speed_interaction'] = np.abs(df['Bz_gsm']) * df['speed'] * (df['Bz_gsm'] < 0)
    df['bz_density_interaction'] = np.abs(df['Bz_gsm']) * df['density'] * (df['Bz_gsm'] < 0)
    df['speed_density_interaction'] = df['speed'] * df['density']
    df['btotal_speed_interaction'] = df['B_total'] * df['speed']
    
    # --- Advanced Derived Features ---
    # Magnetic flux rope indicators
    df['flux_rope_indicator'] = (df['B_total'] > df['B_total'].rolling(60, min_periods=1).mean()) & (df['temperature_depression'] < 0.8)
    
    # Shock indicators
    df['shock_indicator'] = (df['speed_acceleration_10m'] > 50) & (df['density_enhancement'] > 1.5) & (df['btotal_enhancement'] > 1.3)
    
    # Fill NaNs
    df.fillna(method='bfill', inplace=True)
    df.fillna(method='ffill', inplace=True)
    
    # Count new features
    original_features = ['Bz_gsm', 'B_total', 'speed', 'density', 'temperature', 'clock_angle', 'timestamp', 'event_label', 'dynamic_pressure']
    new_features = [col for col in df.columns if col not in original_features]
    
    print(f"✅ Added {len(new_features)} advanced physics-informed features")
    print(f"   - Pressure features: {len([f for f in new_features if 'pressure' in f])}")
    print(f"   - Mach number features: {len([f for f in new_features if 'mach' in f])}")
    print(f"   - Geoeffectiveness features: {len([f for f in new_features if any(x in f for x in ['geoeff', 'coupling', 'electric'])])}")
    print(f"   - Multi-scale features: {len([f for f in new_features if any(x in f for x in ['_5m', '_15m', '_30m', '_60m', '_120m'])])}")
    
    return df


def physics_driven_labeling_enhanced(df: pd.DataFrame, look_ahead: int = 45) -> pd.DataFrame:
    """
    Enhanced physics-driven labeling with adaptive thresholds and multi-criteria consensus.
    """
    print("🎯 Applying enhanced physics-driven labeling...")

    df = df.copy()

    # Adaptive thresholds based on data distribution
    bz_threshold = min(-7.5, df['Bz_gsm'].quantile(0.05))  # At least 5th percentile
    speed_threshold = max(75, df['speed'].diff().quantile(0.95))  # At least 95th percentile
    density_threshold = max(20.0, df['density'].quantile(0.90))  # At least 90th percentile
    btotal_threshold = max(15.0, df['B_total'].quantile(0.85))  # At least 85th percentile

    print(f"   Adaptive thresholds: Bz={bz_threshold:.1f}, Speed={speed_threshold:.1f}, Density={density_threshold:.1f}, Btotal={btotal_threshold:.1f}")

    # Future window analysis
    df['future_bz_peak'] = df['Bz_gsm'].rolling(window=look_ahead, min_periods=1).min()
    df['future_speed_jump'] = df['speed'].diff().rolling(window=look_ahead, min_periods=1).max()
    df['future_density_peak'] = df['density'].rolling(window=look_ahead, min_periods=1).max()
    df['future_btotal_peak'] = df['B_total'].rolling(window=look_ahead, min_periods=1).max()

    # Advanced physics conditions
    df['future_dynamic_pressure'] = (df['density'] * df['speed']**2).rolling(window=look_ahead, min_periods=1).max()
    df['future_southward_duration'] = (df['Bz_gsm'] < -5).rolling(window=look_ahead, min_periods=1).sum()
    df['future_electric_field'] = (df['speed'] * np.abs(df['Bz_gsm']) * (df['Bz_gsm'] < 0)).rolling(window=look_ahead, min_periods=1).max()

    # Multi-criteria consensus scoring
    conditions = {
        'strong_bz': (df['future_bz_peak'] < bz_threshold, 0.30),  # Highest weight
        'speed_shock': (df['future_speed_jump'] > speed_threshold, 0.25),
        'density_spike': (df['future_density_peak'] > density_threshold, 0.20),
        'magnetic_enhancement': (df['future_btotal_peak'] > btotal_threshold, 0.15),
        'sustained_southward': (df['future_southward_duration'] >= 10, 0.10),  # At least 10 minutes
    }

    # Calculate consensus score
    consensus_score = pd.Series(0.0, index=df.index)
    for condition_name, (condition, weight) in conditions.items():
        consensus_score += condition.astype(float) * weight
        print(f"   {condition_name}: {condition.sum()} events ({condition.mean():.3%})")

    # Multi-tier labeling based on consensus score
    high_confidence = consensus_score >= 0.7  # Very high confidence
    medium_confidence = (consensus_score >= 0.5) & (consensus_score < 0.7)  # Medium confidence
    low_confidence = (consensus_score >= 0.3) & (consensus_score < 0.5)  # Low confidence

    # Create labels with confidence levels
    df['event_confidence'] = consensus_score
    df['event_label_raw'] = (consensus_score >= 0.5).astype(int)

    # Apply temporal smoothing and persistence requirements
    df['event_label_smooth'] = df['event_label_raw'].rolling(window=7, min_periods=1, center=True).mean()
    df['event_label'] = (df['event_label_smooth'] > 0.3).astype(int)

    # Shift labels back for prediction window
    df['event_label'] = df['event_label'].shift(-look_ahead).fillna(0).astype(int)
    df['event_confidence'] = df['event_confidence'].shift(-look_ahead).fillna(0)

    # Clean up temporary columns
    temp_cols = [col for col in df.columns if col.startswith('future_') or col.endswith('_raw') or col.endswith('_smooth')]
    df.drop(columns=temp_cols, inplace=True)

    # Report statistics
    positive_count = df['event_label'].sum()
    positive_rate = df['event_label'].mean()
    avg_confidence = df[df['event_label'] == 1]['event_confidence'].mean() if positive_count > 0 else 0

    print(f"✅ Generated {positive_count} positive labels ({positive_rate:.2%})")
    print(f"   Average confidence: {avg_confidence:.3f}")
    print(f"   High confidence events: {(df['event_confidence'] >= 0.7).sum()}")
    print(f"   Medium confidence events: {((df['event_confidence'] >= 0.5) & (df['event_confidence'] < 0.7)).sum()}")

    return df


def optimize_features(df: pd.DataFrame, target_col: str = 'event_label', top_k: int = 50) -> list:
    """
    Use feature importance to select the most relevant physics features.
    """
    print(f"🔍 Optimizing feature selection (top {top_k} features)...")

    # Exclude non-feature columns
    exclude_cols = ['timestamp', 'event_label', 'event_confidence']
    feature_cols = [col for col in df.columns if col not in exclude_cols]

    # Quick feature importance using Random Forest
    X = df[feature_cols].fillna(0)
    y = df[target_col]

    rf = RandomForestClassifier(n_estimators=50, max_depth=10, random_state=42, n_jobs=-1)
    rf.fit(X, y)

    # Get feature importance
    importance_df = pd.DataFrame({
        'feature': feature_cols,
        'importance': rf.feature_importances_
    }).sort_values('importance', ascending=False)

    top_features = importance_df.head(top_k)['feature'].tolist()

    print(f"✅ Selected top {len(top_features)} features")
    print("Top 10 most important features:")
    for i, (_, row) in enumerate(importance_df.head(10).iterrows()):
        print(f"   {i+1:2d}. {row['feature']:<30} ({row['importance']:.4f})")

    return top_features


def prepare_data_complete(data_path: str, config: dict):
    """
    Complete enhanced data preparation pipeline with all optimizations.
    """
    print(f"📊 Loading data from {data_path}")

    # Load data with error handling
    try:
        if data_path.endswith('.parquet'):
            data = pd.read_parquet(data_path)
        elif data_path.endswith('.csv'):
            data = pd.read_csv(data_path)
        else:
            raise ValueError("Unsupported file format. Use .parquet or .csv")
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return None, None, None, None, None

    print(f"📊 Loaded {len(data)} records with {len(data.columns)} columns")

    # Handle timestamp
    if 'timestamp' in data.columns:
        data['timestamp'] = pd.to_datetime(data['timestamp'])
        data = data.sort_values('timestamp').reset_index(drop=True)
        print(f"   Time range: {data['timestamp'].min()} to {data['timestamp'].max()}")

    # Check required columns
    required_cols = ['Bz_gsm', 'B_total', 'speed', 'density', 'temperature']
    missing_cols = [col for col in required_cols if col not in data.columns]
    if missing_cols:
        print(f"❌ Missing required columns: {missing_cols}")
        return None, None, None, None, None

    # Data quality checks
    print("\n🔍 Data quality assessment:")
    for col in required_cols:
        null_pct = data[col].isnull().mean() * 100
        print(f"   {col}: {null_pct:.1f}% null values")
        if null_pct > 50:
            print(f"   ⚠️  Warning: High null percentage in {col}")

    # --- 1. Enhanced Physics-Driven Labeling ---
    data = physics_driven_labeling_enhanced(data, look_ahead=config.get('look_ahead', 45))

    if data['event_label'].sum() == 0:
        print("❌ No positive labels generated. Exiting.")
        return None, None, None, None, None

    # --- 2. Advanced Feature Engineering ---
    data = add_enhanced_physics_features(data)

    # --- 3. Feature Optimization ---
    if config.get('optimize_features', True):
        top_features = optimize_features(data, top_k=config.get('top_k_features', 50))
        available_features = top_features
    else:
        # Use all features except excluded ones
        exclude_cols = ['timestamp', 'event_label', 'event_confidence']
        available_features = [col for col in data.columns if col not in exclude_cols]

    print(f"📊 Using {len(available_features)} optimized features")

    # --- 4. Data Cleaning ---
    initial_len = len(data)
    data = data.dropna(subset=available_features + ['event_label'])
    data.reset_index(drop=True, inplace=True)
    print(f"📊 After cleaning: {len(data)} records (removed {initial_len - len(data)})")

    # --- 5. Temporal Data Splitting ---
    n_total = len(data)
    n_test = int(n_total * config.get('test_size', 0.2))
    n_val = int((n_total - n_test) * config.get('val_size', 0.2))

    train_data = data.iloc[:-n_test-n_val].copy()
    val_data = data.iloc[-n_test-n_val:-n_test].copy()
    test_data = data.iloc[-n_test:].copy()

    print(f"\n📊 Data split: Train={len(train_data)}, Val={len(val_data)}, Test={len(test_data)}")

    # Check class balance
    for split_name, split_data in [('Train', train_data), ('Val', val_data), ('Test', test_data)]:
        pos_rate = split_data['event_label'].mean()
        pos_count = split_data['event_label'].sum()
        print(f"   {split_name}: {pos_count} positive ({pos_rate:.3%})")

    # --- 6. Feature Scaling ---
    scaler = StandardScaler()
    train_data[available_features] = scaler.fit_transform(train_data[available_features])
    val_data[available_features] = scaler.transform(val_data[available_features])
    test_data[available_features] = scaler.transform(test_data[available_features])

    print("✅ Enhanced data preparation complete!")

    return train_data, val_data, test_data, scaler, available_features


# Configuration for enhanced processing
ENHANCED_CONFIG = {
    'test_size': 0.2,
    'val_size': 0.2,
    'look_ahead': 45,
    'optimize_features': True,
    'top_k_features': 50,
    'random_state': 42
}

print("🚀 Enhanced CELEST AI Core Logic Ready!")
print("=" * 50)
print("✅ Advanced physics features")
print("✅ Enhanced consensus labeling")
print("✅ Adaptive thresholds")
print("✅ Feature optimization")
print("✅ Complete data pipeline")
print("=" * 50)
