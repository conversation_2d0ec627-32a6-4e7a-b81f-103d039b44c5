# CELEST AI Training Fix - Enhanced Regularization
# Advanced model with multiple regularization techniques to prevent overfitting

import torch
import torch.nn as nn
import torch.optim as optim
import pytorch_lightning as pl
from sklearn.metrics import f1_score, precision_score, recall_score
import numpy as np

class FocalLoss(nn.Module):
    """Focal Loss for imbalanced classification with label smoothing option"""
    def __init__(self, alpha=None, gamma=2.0, label_smoothing=0.0):
        super(Focal<PERSON>oss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.label_smoothing = label_smoothing

    def forward(self, inputs, targets):
        # Apply label smoothing if specified
        if self.label_smoothing > 0:
            num_classes = inputs.size(1)
            targets_one_hot = torch.zeros_like(inputs)
            targets_one_hot.scatter_(1, targets.unsqueeze(1), 1)
            targets_one_hot = targets_one_hot * (1 - self.label_smoothing) + \
                             self.label_smoothing / num_classes
            ce_loss = -(targets_one_hot * torch.log_softmax(inputs, dim=1)).sum(dim=1)
        else:
            ce_loss = nn.functional.cross_entropy(inputs, targets, reduction='none')
        
        pt = torch.exp(-ce_loss)
        if self.alpha is not None:
            alpha_t = self.alpha[targets]
            focal_loss = alpha_t * (1 - pt)**self.gamma * ce_loss
        else:
            focal_loss = (1 - pt)**self.gamma * ce_loss
        
        return focal_loss.mean()


class ImprovedPatchTSTModel(pl.LightningModule):
    """Enhanced PatchTST with advanced regularization techniques"""
    
    def __init__(self, config, n_features, class_weights):
        super().__init__()
        self.save_hyperparameters()

        # Patching logic
        self.n_patches = config['sequence_length'] // config['patch_size']
        self.patch_embedding = nn.Linear(config['patch_size'] * n_features, config['d_model'])
        
        # Enhanced dropout strategies
        self.patch_dropout = nn.Dropout(config['dropout'])
        self.embedding_dropout = nn.Dropout(config['dropout'] * 0.5)  # Lighter dropout for embeddings

        # Positional encoding with initialization
        self.pos_encoding = nn.Parameter(torch.randn(1, self.n_patches, config['d_model']) * 0.02)

        # Enhanced Transformer Encoder with stochastic depth
        encoder_layers = []
        for i in range(config['n_layers']):
            # Gradually increase dropout in deeper layers
            layer_dropout = config['dropout'] * (1 + 0.1 * i)
            encoder_layer = nn.TransformerEncoderLayer(
                d_model=config['d_model'], 
                nhead=config['n_heads'],
                dim_feedforward=config['d_model'] * 4, 
                dropout=min(layer_dropout, 0.5),  # Cap at 0.5
                batch_first=True, 
                activation='gelu'
            )
            encoder_layers.append(encoder_layer)
        
        self.transformer = nn.ModuleList(encoder_layers)
        
        # Enhanced Classification Head with residual connection
        self.pre_head_norm = nn.LayerNorm(config['d_model'])
        self.head = nn.Sequential(
            nn.Dropout(config['dropout']),
            nn.Linear(config['d_model'], config['d_model'] // 2),
            nn.GELU(),
            nn.LayerNorm(config['d_model'] // 2),
            nn.Dropout(config['dropout'] * 0.8),
            nn.Linear(config['d_model'] // 2, config['d_model'] // 4),
            nn.GELU(),
            nn.Dropout(config['dropout'] * 0.6),
            nn.Linear(config['d_model'] // 4, 2)
        )

        # Enhanced Loss Function with label smoothing
        if config.get('use_focal_loss', True):
            self.criterion = FocalLoss(
                alpha=class_weights, 
                gamma=config.get('focal_loss_gamma', 2.0),
                label_smoothing=config.get('label_smoothing', 0.1)
            )
        else:
            self.criterion = nn.CrossEntropyLoss(
                weight=class_weights, 
                label_smoothing=config.get('label_smoothing', 0.1)
            )
        
        self.validation_step_outputs = []
        
        # Training stability
        self.automatic_optimization = True

    def forward(self, x):
        batch_size = x.shape[0]
        
        # Patching with enhanced dropout
        x = x.view(batch_size, self.n_patches, 
                  self.hparams.config['patch_size'] * self.hparams.n_features)
        
        # Embedding with dropout
        x = self.patch_embedding(x)
        x = self.embedding_dropout(x)
        
        # Add positional encoding
        x = x + self.pos_encoding
        x = self.patch_dropout(x)
        
        # Enhanced transformer with stochastic depth
        for i, layer in enumerate(self.transformer):
            # Apply stochastic depth during training
            if self.training and torch.rand(1) < 0.1 * i / len(self.transformer):
                continue  # Skip this layer with probability
            x = layer(x)
        
        # Global average pooling instead of just last token
        x = x.mean(dim=1)  # Average across sequence dimension
        
        # Pre-head normalization
        x = self.pre_head_norm(x)
        
        # Classification head
        return self.head(x)

    def training_step(self, batch, batch_idx):
        x, y = batch
        logits = self(x)
        loss = self.criterion(logits, y)
        
        # Enhanced L2 regularization with different weights for different layers
        l2_reg = torch.tensor(0., device=self.device)
        for name, param in self.named_parameters():
            if 'weight' in name:
                if 'transformer' in name:
                    l2_reg += 0.0005 * torch.norm(param)  # Lighter for transformer
                elif 'head' in name:
                    l2_reg += 0.001 * torch.norm(param)   # Heavier for head
                else:
                    l2_reg += 0.0001 * torch.norm(param)  # Light for embeddings
        
        total_loss = loss + l2_reg
        
        # Log metrics
        self.log('train_loss', total_loss, on_step=True, on_epoch=True, prog_bar=True)
        self.log('train_ce_loss', loss, on_step=False, on_epoch=True)
        self.log('train_l2_reg', l2_reg, on_step=False, on_epoch=True)
        
        return total_loss

    def validation_step(self, batch, batch_idx):
        x, y = batch
        logits = self(x)
        loss = self.criterion(logits, y)
        preds = torch.argmax(logits, dim=1)
        
        # Store outputs for epoch-end calculation
        self.validation_step_outputs.append({
            'loss': loss, 
            'preds': preds, 
            'targets': y,
            'logits': logits
        })
        return loss

    def on_validation_epoch_end(self):
        # Calculate average loss
        avg_loss = torch.stack([x['loss'] for x in self.validation_step_outputs]).mean()
        
        # Concatenate all predictions and targets
        all_preds = torch.cat([x['preds'] for x in self.validation_step_outputs])
        all_targets = torch.cat([x['targets'] for x in self.validation_step_outputs])
        all_logits = torch.cat([x['logits'] for x in self.validation_step_outputs])
        
        # Calculate metrics
        val_f1 = f1_score(all_targets.cpu(), all_preds.cpu(), zero_division=0)
        val_precision = precision_score(all_targets.cpu(), all_preds.cpu(), zero_division=0)
        val_recall = recall_score(all_targets.cpu(), all_preds.cpu(), zero_division=0)
        
        # Calculate confidence metrics
        probs = torch.softmax(all_logits, dim=1)
        confidence = probs.max(dim=1)[0].mean()
        
        # Log all metrics
        self.log_dict({
            'val_loss': avg_loss,
            'val_f1': val_f1,
            'val_precision': val_precision,
            'val_recall': val_recall,
            'val_confidence': confidence
        }, prog_bar=True, logger=True)
        
        # Clear outputs
        self.validation_step_outputs.clear()

    def configure_optimizers(self):
        # Enhanced optimizer with different learning rates for different components
        param_groups = [
            {
                'params': [p for n, p in self.named_parameters() if 'transformer' in n],
                'lr': self.hparams.config['learning_rate'] * 0.5,  # Lower LR for transformer
                'weight_decay': 0.01
            },
            {
                'params': [p for n, p in self.named_parameters() if 'head' in n],
                'lr': self.hparams.config['learning_rate'] * 0.8,  # Higher LR for head
                'weight_decay': 0.02
            },
            {
                'params': [p for n, p in self.named_parameters() if 'embedding' in n or 'pos_encoding' in n],
                'lr': self.hparams.config['learning_rate'] * 0.3,  # Lowest LR for embeddings
                'weight_decay': 0.005
            }
        ]
        
        optimizer = optim.AdamW(param_groups)
        
        # Enhanced scheduler with warmup
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, 
            mode='max', 
            factor=0.5, 
            patience=self.hparams.config.get('lr_scheduler_patience', 2),
            verbose=True,
            min_lr=1e-6
        )
        
        return {
            'optimizer': optimizer,
            'lr_scheduler': {
                'scheduler': scheduler, 
                'monitor': 'val_f1',
                'frequency': 1
            }
        }


# Enhanced Configuration
def get_enhanced_config(base_config):
    """Get enhanced configuration with improved regularization"""
    enhanced_config = base_config.copy()
    enhanced_config.update({
        # Regularization
        'dropout': 0.25,  # Balanced dropout
        'label_smoothing': 0.1,  # Label smoothing
        
        # Learning
        'learning_rate': 3e-5,  # Conservative learning rate
        'early_stopping_patience': 4,  # Earlier stopping
        'lr_scheduler_patience': 2,  # Faster LR reduction
        
        # Loss function
        'use_focal_loss': True,
        'focal_loss_gamma': 1.5,  # Less aggressive focusing
        
        # Training stability
        'gradient_clip_val': 0.5,  # Gradient clipping
        'max_epochs': 25,  # Fewer epochs to prevent overfitting
    })
    return enhanced_config

print("🔧 Enhanced PatchTST model with advanced regularization created!")
print("Key improvements:")
print("- Multi-level dropout strategy")
print("- Stochastic depth in transformer")
print("- Global average pooling")
print("- Enhanced L2 regularization")
print("- Label smoothing")
print("- Differential learning rates")
print("- Gradient clipping")
