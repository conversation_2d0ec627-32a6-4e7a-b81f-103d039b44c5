#!/usr/bin/env python3
"""
CELEST AI Complete Production Fix

This script provides a comprehensive fix for all identified issues in the
CELEST AI world-class production notebook:

1. Severe class imbalance (99.99% vs 0.01%)
2. Missing test_step() method
3. PDCE thresholds too strict
4. Training instability

Usage:
    # In your notebook, run:
    exec(open('notebooks/celest_ai_complete_fix.py').read())
    
    # Then apply the complete fix:
    fixed_data, fixed_model, fixed_config = apply_complete_celest_fix(enhanced_df, model)
"""

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
from sklearn.metrics import f1_score, precision_score, recall_score, roc_auc_score, average_precision_score
from sklearn.utils.class_weight import compute_class_weight
import matplotlib.pyplot as plt
import seaborn as sns
import types

print("🚀 CELEST AI Complete Production Fix")
print("=" * 50)

# 🔧 Fix 1: Enhanced PDCE with Multiple Threshold Levels
class ProductionPDCE:
    """Production-ready PDCE with adaptive thresholds"""
    
    def __init__(self, threshold_level='lenient'):
        """
        Initialize PDCE with different threshold levels
        
        Args:
            threshold_level: 'strict', 'moderate', 'lenient', or 'adaptive'
        """
        self.threshold_configs = {
            'strict': {
                'bz_threshold': -5.0,
                'bt_threshold': 10.0,
                'speed_threshold': 450.0,
                'density_threshold': 5.0
            },
            'moderate': {
                'bz_threshold': -4.0,
                'bt_threshold': 9.0,
                'speed_threshold': 425.0,
                'density_threshold': 4.0
            },
            'lenient': {
                'bz_threshold': -3.0,
                'bt_threshold': 8.0,
                'speed_threshold': 400.0,
                'density_threshold': 3.0
            },
            'adaptive': {
                'bz_threshold': -2.5,
                'bt_threshold': 7.0,
                'speed_threshold': 380.0,
                'density_threshold': 2.5
            }
        }
        
        self.config = self.threshold_configs[threshold_level]
        self.threshold_level = threshold_level
        
        print(f"🔧 Production PDCE initialized with {threshold_level} thresholds:")
        for param, value in self.config.items():
            print(f"   {param}: {value}")
    
    def generate_labels(self, df):
        """Generate labels with production-quality logic"""
        print(f"🔬 Generating labels with {self.threshold_level} PDCE...")
        
        # Multi-condition approach
        conditions = {
            'bz_south': df['bz_gsm'] < self.config['bz_threshold'],
            'bt_enhanced': df['bt'] > self.config['bt_threshold'],
            'speed_high': df['speed'] > self.config['speed_threshold'],
            'density_enhanced': df['density'] > self.config['density_threshold']
        }
        
        # Calculate dynamic pressure
        dynamic_pressure = 1.67e-6 * df['density'] * df['speed']**2
        conditions['dynamic_pressure'] = dynamic_pressure > 1.5
        
        # Consensus scoring (more lenient)
        primary_score = sum([
            conditions['bz_south'].astype(int),
            conditions['bt_enhanced'].astype(int),
            conditions['speed_high'].astype(int),
            conditions['density_enhanced'].astype(int)
        ])
        
        # More lenient consensus: 2 out of 4 conditions OR dynamic pressure
        consensus = (primary_score >= 2) | conditions['dynamic_pressure']
        
        # Apply lookhead window
        labels = np.zeros(len(df), dtype=int)
        lookhead = 45  # 45-minute lookhead
        
        for i in range(len(df) - lookhead):
            if consensus.iloc[i:i+lookhead].any():
                labels[i] = 1
        
        # Create result dataframe
        result_df = df.copy()
        result_df['event_label'] = labels
        result_df['consensus_score'] = primary_score
        
        event_rate = labels.mean() * 100
        print(f"   ✅ Event rate: {event_rate:.2f}%")
        
        return result_df

# 🎯 Fix 2: Advanced Class Balancing
def balance_dataset(df, target_ratio=0.15, method='augmentation'):
    """Balance dataset using various techniques"""
    print(f"📊 Balancing dataset to {target_ratio*100:.1f}% event rate...")
    
    current_rate = df['event_label'].mean()
    print(f"   Current event rate: {current_rate*100:.2f}%")
    
    if current_rate >= target_ratio:
        print("   ✅ Dataset already balanced")
        return df
    
    minority_samples = df[df['event_label'] == 1]
    majority_samples = df[df['event_label'] == 0]
    
    if len(minority_samples) == 0:
        print("   ❌ No minority samples found")
        return df
    
    # Calculate target counts
    target_minority = int(len(majority_samples) * target_ratio / (1 - target_ratio))
    needed_samples = max(0, target_minority - len(minority_samples))
    
    if needed_samples > 0:
        print(f"   Adding {needed_samples} synthetic samples...")
        
        # Generate synthetic samples with noise
        synthetic_samples = []
        for _ in range(needed_samples):
            base_sample = minority_samples.sample(1).copy()
            
            # Add controlled noise to numeric columns
            numeric_cols = base_sample.select_dtypes(include=[np.number]).columns
            for col in numeric_cols:
                if col not in ['event_label', 'consensus_score']:
                    noise_std = base_sample[col].std() * 0.05  # 5% noise
                    noise = np.random.normal(0, noise_std)
                    base_sample[col] += noise
            
            synthetic_samples.append(base_sample)
        
        # Combine datasets
        balanced_df = pd.concat([df] + synthetic_samples, ignore_index=True)
        
        new_rate = balanced_df['event_label'].mean() * 100
        print(f"   ✅ New event rate: {new_rate:.2f}%")
        
        return balanced_df
    
    return df

# 🔥 Fix 3: Production Loss Function
class ProductionFocalLoss(nn.Module):
    """Production-optimized Focal Loss"""
    
    def __init__(self, alpha=None, gamma=2.5, label_smoothing=0.1):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.label_smoothing = label_smoothing
    
    def forward(self, inputs, targets):
        # Cross entropy with label smoothing
        ce_loss = F.cross_entropy(inputs, targets, reduction='none', label_smoothing=self.label_smoothing)
        pt = torch.exp(-ce_loss)
        
        # Apply alpha weighting
        if self.alpha is not None:
            alpha_t = self.alpha[targets]
            focal_loss = alpha_t * (1 - pt)**self.gamma * ce_loss
        else:
            focal_loss = (1 - pt)**self.gamma * ce_loss
        
        return focal_loss.mean()

# 🧠 Fix 4: Add Missing Test Methods
def add_production_test_methods(model):
    """Add comprehensive test methods to model"""
    
    def test_step(self, batch, batch_idx):
        x, y = batch
        logits = self(x)
        loss = self.criterion(logits, y)
        preds = torch.argmax(logits, dim=1)
        
        if not hasattr(self, 'test_step_outputs'):
            self.test_step_outputs = []
        
        self.test_step_outputs.append({
            'loss': loss,
            'preds': preds,
            'targets': y,
            'logits': logits
        })
        
        return loss
    
    def on_test_epoch_end(self):
        if not hasattr(self, 'test_step_outputs') or not self.test_step_outputs:
            return
        
        # Calculate comprehensive metrics
        avg_loss = torch.stack([x['loss'] for x in self.test_step_outputs]).mean()
        all_preds = torch.cat([x['preds'] for x in self.test_step_outputs])
        all_targets = torch.cat([x['targets'] for x in self.test_step_outputs])
        all_logits = torch.cat([x['logits'] for x in self.test_step_outputs])
        
        # Convert to numpy
        preds_np = all_preds.cpu().numpy()
        targets_np = all_targets.cpu().numpy()
        probs = torch.softmax(all_logits, dim=1)
        probs_np = probs.cpu().numpy()
        
        # Calculate metrics
        test_f1 = f1_score(targets_np, preds_np, zero_division=0)
        test_precision = precision_score(targets_np, preds_np, zero_division=0)
        test_recall = recall_score(targets_np, preds_np, zero_division=0)
        
        # Calculate AUC and AP if possible
        try:
            if len(np.unique(targets_np)) > 1:
                test_auc = roc_auc_score(targets_np, probs_np[:, 1])
                test_ap = average_precision_score(targets_np, probs_np[:, 1])
            else:
                test_auc = 0.0
                test_ap = 0.0
        except:
            test_auc = 0.0
            test_ap = 0.0
        
        # Log metrics
        self.log_dict({
            'test_loss': avg_loss,
            'test_f1': test_f1,
            'test_precision': test_precision,
            'test_recall': test_recall,
            'test_auc': test_auc,
            'test_ap': test_ap
        })
        
        # Store results
        self.test_results = {
            'loss': avg_loss.item(),
            'f1': test_f1,
            'precision': test_precision,
            'recall': test_recall,
            'auc': test_auc,
            'average_precision': test_ap,
            'predictions': preds_np,
            'targets': targets_np,
            'probabilities': probs_np
        }
        
        print(f"\n📊 Production Test Results:")
        print(f"   F1 Score: {test_f1:.4f}")
        print(f"   Precision: {test_precision:.4f}")
        print(f"   Recall: {test_recall:.4f}")
        print(f"   AUC: {test_auc:.4f}")
        print(f"   Average Precision: {test_ap:.4f}")
        
        self.test_step_outputs.clear()
    
    # Add methods to model
    model.test_step = types.MethodType(test_step, model)
    model.on_test_epoch_end = types.MethodType(on_test_epoch_end, model)
    
    return model

# 📊 Fix 5: Production Training Configuration
def get_production_config():
    """Get production-optimized training configuration"""
    return {
        'batch_size': 32,
        'learning_rate': 1e-5,
        'max_epochs': 50,
        'dropout': 0.3,
        'gradient_clip_val': 0.5,
        'early_stopping_patience': 8,
        'lr_scheduler_patience': 3,
        'focal_loss_gamma': 2.5,
        'label_smoothing': 0.1,
        'num_workers': 0,
        'pin_memory': False
    }

# 🚀 Complete Fix Application
def apply_complete_celest_fix(df, model=None, threshold_level='lenient'):
    """Apply all fixes to create production-ready CELEST AI"""
    print("🚀 Applying Complete CELEST AI Production Fix...")
    
    # Step 1: Fix data labeling
    print("\n1️⃣ Fixing data labeling...")
    pdce = ProductionPDCE(threshold_level=threshold_level)
    df_labeled = pdce.generate_labels(df)
    
    # Step 2: Balance dataset
    print("\n2️⃣ Balancing dataset...")
    df_balanced = balance_dataset(df_labeled, target_ratio=0.12)
    
    # Step 3: Add test methods to model
    if model is not None:
        print("\n3️⃣ Adding test methods to model...")
        model = add_production_test_methods(model)
    
    # Step 4: Get production config
    print("\n4️⃣ Getting production configuration...")
    config = get_production_config()
    
    # Step 5: Calculate class weights
    print("\n5️⃣ Calculating production class weights...")
    y_values = df_balanced['event_label'].values
    unique_classes = np.unique(y_values)
    
    if len(unique_classes) > 1:
        class_weights = compute_class_weight('balanced', classes=unique_classes, y=y_values)
        class_weights = torch.FloatTensor(class_weights)
    else:
        class_weights = torch.FloatTensor([1.0, 10.0])
    
    print(f"   Class weights: {class_weights.numpy()}")
    
    print("\n✅ Complete fix applied successfully!")
    print(f"   Final event rate: {df_balanced['event_label'].mean()*100:.2f}%")
    print(f"   Dataset size: {len(df_balanced):,} samples")
    
    return df_balanced, model, config, class_weights

# 📈 Visualization Function
def plot_fix_results(df_original, df_fixed):
    """Plot before/after comparison"""
    fig, axes = plt.subplots(1, 2, figsize=(12, 5))
    
    # Original distribution
    original_counts = df_original['event_label'].value_counts()
    axes[0].pie(original_counts.values, labels=['Normal', 'CME'], autopct='%1.2f%%')
    axes[0].set_title('Original Class Distribution')
    
    # Fixed distribution
    fixed_counts = df_fixed['event_label'].value_counts()
    axes[1].pie(fixed_counts.values, labels=['Normal', 'CME'], autopct='%1.2f%%')
    axes[1].set_title('Fixed Class Distribution')
    
    plt.tight_layout()
    plt.show()
    
    print(f"📊 Distribution Comparison:")
    print(f"   Original: {original_counts.values}")
    print(f"   Fixed: {fixed_counts.values}")

if __name__ == "__main__":
    print("🔧 CELEST AI Complete Production Fix Ready")
    print("\nUsage:")
    print("   df_fixed, model_fixed, config, weights = apply_complete_celest_fix(enhanced_df, model)")
