# ==============================================================================
# 🔬 CELEST AI: Core Logic Implementation for Kaggle
# Physics-Driven Feature Engineering and Labeling Functions
# 
# Copy this entire code block into a new cell in your Kaggle notebook
# Place it BEFORE the "Data Loading and Preparation" cell
# ==============================================================================

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from sklearn.preprocessing import StandardScaler

def add_enhanced_physics_features(df: pd.DataFrame) -> pd.DataFrame:
    """
    Engineers a rich set of physics-informed features to help the model identify
    CME signatures, mimicking the logic of the Physics-Driven Consensus Engine (PDCE).

    Args:
        df (pd.DataFrame): Input dataframe with basic solar wind features.

    Returns:
        pd.DataFrame: Dataframe with new, engineered features.
    """
    print('🔬 Engineering physics-informed features...')
    
    # --- Bz (Southward Magnetic Field) Features ---
    # The most critical indicator for geo-effective CMEs.
    df['bz_southward_persistence'] = (df['Bz_gsm'] < 0).rolling(window=30, min_periods=1).mean()
    df['bz_intensity_30m'] = df['Bz_gsm'].rolling(window=30, min_periods=1).min()
    df['bz_to_btotal_ratio'] = df['Bz_gsm'] / (df['B_total'] + 1e-6)
    
    # --- Speed Features ---
    # Shock fronts are characterized by sharp increases in speed.
    df['speed_acceleration_10m'] = df['speed'].diff(periods=10)
    df['speed_enhancement'] = df['speed'] / df['speed'].rolling(window=60, min_periods=1).mean()
    
    # --- Density Features ---
    # CMEs often involve a pile-up of plasma, leading to a density spike.
    df['density_enhancement'] = df['density'] / df['density'].rolling(window=60, min_periods=1).mean()
    
    # --- Pressure and Energy Ratios ---
    # Dynamic pressure is a direct measure of the solar wind's impact force.
    # The ratio of kinetic to magnetic pressure helps distinguish CME types.
    df['kinetic_to_magnetic_pressure'] = (df['density'] * df['speed']**2) / (df['B_total']**2 + 1e-6)
    
    # --- Combined Geo-effectiveness Proxy (PDCE-like logic) ---
    # A single powerful feature that combines the most critical indicators.
    # A strong, sustained southward Bz is the primary driver.
    # This is weighted by dynamic pressure, as a high-pressure event is more impactful.
    df['geoeffectiveness_proxy'] = -1 * df['Bz_gsm'] * (df['Bz_gsm'] < 0) * df['dynamic_pressure']
    
    # Fill NaNs created by rolling windows/diffs using backfill
    df.fillna(method='bfill', inplace=True)
    df.fillna(method='ffill', inplace=True)  # Fill any remaining at the start
    
    print(f'✅ Added {len([c for c in df.columns if any(x in c for x in ["_enhancement", "_ratio", "_pressure", "_proxy"])])} physics features')
    return df


def physics_driven_labeling(df: pd.DataFrame, look_ahead: int = 45) -> pd.DataFrame:
    """
    Applies a robust, physics-driven labeling logic to create a high-quality
    target variable. An event is labeled based on conditions in a future window.

    Args:
        df (pd.DataFrame): The dataframe with solar wind data.
        look_ahead (int): The window (in minutes) to look ahead for a CME peak.

    Returns:
        pd.DataFrame: The dataframe with a new 'event_label' column.
    """
    print('🎯 Applying robust physics-driven labeling...')
    
    # Define thresholds based on solar physics for a high-confidence event
    BZ_THRESHOLD = -7.5  # Very strong southward Bz
    SPEED_JUMP_THRESHOLD = 75  # Significant speed increase (km/s)
    DENSITY_THRESHOLD = 20.0   # High density (n/cm^3)
    
    # Look ahead in time to find the peak of a potential event
    df['future_bz_peak'] = df['Bz_gsm'].rolling(window=look_ahead, min_periods=1).min()
    df['future_speed_jump'] = df['speed'].diff().rolling(window=look_ahead, min_periods=1).max()
    df['future_density_peak'] = df['density'].rolling(window=look_ahead, min_periods=1).max()
    
    # Define the consensus condition for a high-confidence event
    is_cme_imminent = (
        (df['future_bz_peak'] < BZ_THRESHOLD) &
        (df['future_speed_jump'] > SPEED_JUMP_THRESHOLD) &
        (df['future_density_peak'] > DENSITY_THRESHOLD)
    )
    
    # Shift the labels back so the model has a warning window
    # We are labeling time 't' as 1 if a CME peak occurs between t and t+look_ahead
    df['event_label'] = is_cme_imminent.shift(-look_ahead).fillna(0).astype(int)
    
    # Clean up temporary columns
    df.drop(columns=['future_bz_peak', 'future_speed_jump', 'future_density_peak'], inplace=True)
    
    print(f'✅ Generated {df["event_label"].sum()} positive labels ({df["event_label"].mean():.2%})')
    return df


class PhysicsInformedLoss(nn.Module):
    """
    Physics-informed loss function for CME detection
    Combines focal loss for hard example mining with class balancing
    """
    def __init__(self, class_weights=None, alpha=0.25, gamma=2.0):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.class_weights = class_weights
    
    def forward(self, inputs, targets):
        # Focal loss for hard example mining
        ce_loss = F.cross_entropy(inputs, targets, weight=self.class_weights, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss
        return focal_loss.mean()


def prepare_data_enhanced(data_path: str, config: dict):
    """
    Enhanced data preparation with physics-driven labeling and feature engineering
    """
    print(f"Loading data from {data_path}")
    
    # Load data
    data = pd.read_parquet(data_path)
    if 'timestamp' in data.columns:
        data['timestamp'] = pd.to_datetime(data['timestamp'])
        data = data.sort_values('timestamp').reset_index(drop=True)
    print(f"Loaded {len(data)} records")
    
    # Display data info
    print("\nData Info:")
    if 'timestamp' in data.columns:
        print(f"Date range: {data['timestamp'].min()} to {data['timestamp'].max()}")
    print(f"Columns: {list(data.columns)}")
    
    # --- 1. Physics-Driven Labeling (CORE LOGIC) ---
    # Use the robust function to create high-quality labels FIRST.
    data = physics_driven_labeling(data, look_ahead=45)
    
    # Check target distribution
    target_dist = data['event_label'].value_counts()
    print(f"\n🎯 Target distribution post-PDCE:")
    print(target_dist)
    if 1 in target_dist:
        print(f"Positive rate: {target_dist[1] / len(data):.3%}")
    else:
        print("⚠️ Warning: No positive labels generated. Check thresholds in physics_driven_labeling.")

    # --- 2. Feature Engineering (CORE LOGIC) ---
    print("\n🔬 Adding enhanced physics-informed features...")
    data = add_enhanced_physics_features(data)

    # Define all features to be used
    base_features = [
        'Bz_gsm', 'B_total', 'speed', 'density', 'temperature', 'clock_angle'
    ]
    physics_features = [
        'bz_southward_persistence', 'bz_intensity_30m', 'bz_to_btotal_ratio',
        'speed_acceleration_10m', 'speed_enhancement', 'density_enhancement',
        'dynamic_pressure', 'kinetic_to_magnetic_pressure', 'geoeffectiveness_proxy'
    ]
    feature_columns = base_features + physics_features
    available_features = [col for col in feature_columns if col in data.columns]
    print(f"📊 Total features used: {len(available_features)}")
    print(f"Features: {available_features}")
    
    # Handle missing values after all engineering is done
    data = data.dropna(subset=available_features + ['event_label'])
    print(f"After removing NaN: {len(data)} records")

    # --- 3. Data Splitting and Scaling ---
    # Temporal split (important for time series)
    n_total = len(data)
    n_test = int(n_total * config['test_size'])
    n_val = int((n_total - n_test) * config['val_size'])
    
    train_data = data.iloc[:-n_test-n_val]
    val_data = data.iloc[-n_test-n_val:-n_test]
    test_data = data.iloc[-n_test:]
    
    print(f"\n📊 Data split: Train={len(train_data)}, Val={len(val_data)}, Test={len(test_data)}")
    
    # Scale features
    scaler = StandardScaler()
    train_data_scaled = train_data.copy()
    val_data_scaled = val_data.copy()
    test_data_scaled = test_data.copy()
    
    train_data_scaled[available_features] = scaler.fit_transform(train_data[available_features])
    val_data_scaled[available_features] = scaler.transform(val_data[available_features])
    test_data_scaled[available_features] = scaler.transform(test_data[available_features])
    
    return train_data_scaled, val_data_scaled, test_data_scaled, scaler, available_features

print("✅ Core logic functions loaded successfully!")
print("🚀 Ready to implement physics-driven CELEST AI training!")
