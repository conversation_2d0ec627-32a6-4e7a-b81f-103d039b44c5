#!/usr/bin/env python3
"""
Quick Fix for Missing test_step() Method

This script provides the missing test_step and on_test_epoch_end methods
for the ProductionPatchTSTModel class.

Usage:
    # In your notebook, run this before testing:
    exec(open('notebooks/test_step_fix.py').read())
"""

import torch
import numpy as np
from sklearn.metrics import f1_score, precision_score, recall_score, roc_auc_score, average_precision_score

def add_test_methods_to_model(model):
    """Add missing test methods to the model"""
    
    def test_step(self, batch, batch_idx):
        """Test step for model evaluation"""
        x, y = batch
        logits = self(x)
        loss = self.criterion(logits, y)
        preds = torch.argmax(logits, dim=1)
        
        # Store results for epoch end calculation
        if not hasattr(self, 'test_step_outputs'):
            self.test_step_outputs = []
        
        self.test_step_outputs.append({
            'loss': loss, 
            'preds': preds, 
            'targets': y,
            'logits': logits
        })
        
        return loss

    def on_test_epoch_end(self):
        """Calculate comprehensive test metrics at epoch end"""
        if not hasattr(self, 'test_step_outputs') or not self.test_step_outputs:
            print("⚠️ No test outputs found")
            return
        
        print("📊 Calculating comprehensive test metrics...")
        
        # Calculate metrics
        avg_loss = torch.stack([x['loss'] for x in self.test_step_outputs]).mean()
        all_preds = torch.cat([x['preds'] for x in self.test_step_outputs])
        all_targets = torch.cat([x['targets'] for x in self.test_step_outputs])
        all_logits = torch.cat([x['logits'] for x in self.test_step_outputs])
        
        # Convert to numpy for sklearn metrics
        preds_np = all_preds.cpu().numpy()
        targets_np = all_targets.cpu().numpy()
        probs = torch.softmax(all_logits, dim=1)
        probs_np = probs.cpu().numpy()
        
        # Calculate comprehensive test metrics
        test_f1 = f1_score(targets_np, preds_np, zero_division=0)
        test_precision = precision_score(targets_np, preds_np, zero_division=0)
        test_recall = recall_score(targets_np, preds_np, zero_division=0)
        
        # Calculate confidence metrics
        confidence = probs.max(dim=1)[0].mean()
        
        # Calculate additional metrics for imbalanced data
        try:
            if len(np.unique(targets_np)) > 1:
                test_auc = roc_auc_score(targets_np, probs_np[:, 1])
                test_ap = average_precision_score(targets_np, probs_np[:, 1])
            else:
                test_auc = 0.0
                test_ap = 0.0
        except Exception as e:
            print(f"⚠️ Could not calculate AUC/AP: {e}")
            test_auc = 0.0
            test_ap = 0.0
        
        # Calculate class-wise metrics
        unique_classes = np.unique(targets_np)
        class_metrics = {}
        
        for cls in unique_classes:
            cls_mask = targets_np == cls
            cls_pred_mask = preds_np == cls
            
            # True positives, false positives, false negatives
            tp = np.sum(cls_mask & cls_pred_mask)
            fp = np.sum(~cls_mask & cls_pred_mask)
            fn = np.sum(cls_mask & ~cls_pred_mask)
            
            cls_precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
            cls_recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
            cls_f1 = 2 * cls_precision * cls_recall / (cls_precision + cls_recall) if (cls_precision + cls_recall) > 0 else 0.0
            
            class_metrics[f'class_{cls}'] = {
                'precision': cls_precision,
                'recall': cls_recall,
                'f1': cls_f1,
                'support': np.sum(cls_mask)
            }
        
        # Log test metrics
        self.log_dict({
            'test_loss': avg_loss,
            'test_f1': test_f1,
            'test_precision': test_precision,
            'test_recall': test_recall,
            'test_confidence': confidence,
            'test_auc': test_auc,
            'test_average_precision': test_ap
        }, prog_bar=True, logger=True)
        
        # Store comprehensive results for detailed analysis
        self.test_results = {
            'loss': avg_loss.item(),
            'f1': test_f1,
            'precision': test_precision,
            'recall': test_recall,
            'auc': test_auc,
            'average_precision': test_ap,
            'confidence': confidence.item(),
            'predictions': preds_np,
            'targets': targets_np,
            'probabilities': probs_np,
            'class_metrics': class_metrics
        }
        
        # Print detailed results
        print(f"\n📊 Test Results Summary:")
        print(f"   Loss: {avg_loss.item():.4f}")
        print(f"   F1 Score: {test_f1:.4f}")
        print(f"   Precision: {test_precision:.4f}")
        print(f"   Recall: {test_recall:.4f}")
        print(f"   AUC: {test_auc:.4f}")
        print(f"   Average Precision: {test_ap:.4f}")
        print(f"   Confidence: {confidence.item():.4f}")
        
        print(f"\n📈 Class-wise Metrics:")
        for cls_name, metrics in class_metrics.items():
            print(f"   {cls_name}: P={metrics['precision']:.3f}, R={metrics['recall']:.3f}, F1={metrics['f1']:.3f}, Support={metrics['support']}")
        
        # Analyze class distribution
        class_dist = np.bincount(targets_np)
        print(f"\n📊 Test Set Class Distribution:")
        for i, count in enumerate(class_dist):
            percentage = count / len(targets_np) * 100
            print(f"   Class {i}: {count} samples ({percentage:.2f}%)")
        
        self.test_step_outputs.clear()
    
    # Add methods to the model
    import types
    model.test_step = types.MethodType(test_step, model)
    model.on_test_epoch_end = types.MethodType(on_test_epoch_end, model)
    
    print("✅ Test methods added to model")
    return model

def create_comprehensive_evaluation(model, test_loader):
    """Create comprehensive evaluation without using trainer.test()"""
    print("🔍 Running comprehensive model evaluation...")
    
    model.eval()
    all_preds = []
    all_targets = []
    all_probs = []
    all_losses = []
    
    with torch.no_grad():
        for batch_idx, batch in enumerate(test_loader):
            x, y = batch
            
            # Move to device if needed
            if hasattr(model, 'device'):
                x = x.to(model.device)
                y = y.to(model.device)
            
            # Forward pass
            logits = model(x)
            loss = model.criterion(logits, y)
            preds = torch.argmax(logits, dim=1)
            probs = torch.softmax(logits, dim=1)
            
            # Store results
            all_preds.append(preds.cpu())
            all_targets.append(y.cpu())
            all_probs.append(probs.cpu())
            all_losses.append(loss.cpu())
            
            if batch_idx % 50 == 0:
                print(f"   Processed {batch_idx + 1}/{len(test_loader)} batches")
    
    # Concatenate all results
    all_preds = torch.cat(all_preds).numpy()
    all_targets = torch.cat(all_targets).numpy()
    all_probs = torch.cat(all_probs).numpy()
    avg_loss = torch.stack(all_losses).mean().item()
    
    # Calculate comprehensive metrics
    f1 = f1_score(all_targets, all_preds, zero_division=0)
    precision = precision_score(all_targets, all_preds, zero_division=0)
    recall = recall_score(all_targets, all_preds, zero_division=0)
    
    try:
        if len(np.unique(all_targets)) > 1:
            auc = roc_auc_score(all_targets, all_probs[:, 1])
            ap = average_precision_score(all_targets, all_probs[:, 1])
        else:
            auc = 0.0
            ap = 0.0
    except:
        auc = 0.0
        ap = 0.0
    
    results = {
        'loss': avg_loss,
        'f1': f1,
        'precision': precision,
        'recall': recall,
        'auc': auc,
        'average_precision': ap,
        'predictions': all_preds,
        'targets': all_targets,
        'probabilities': all_probs
    }
    
    print(f"\n✅ Comprehensive Evaluation Complete:")
    print(f"   Loss: {avg_loss:.4f}")
    print(f"   F1 Score: {f1:.4f}")
    print(f"   Precision: {precision:.4f}")
    print(f"   Recall: {recall:.4f}")
    print(f"   AUC: {auc:.4f}")
    print(f"   Average Precision: {ap:.4f}")
    
    return results

# Usage instructions
def print_usage():
    """Print usage instructions"""
    print("\n🔧 Test Step Fix Usage:")
    print("   1. Add test methods: model = add_test_methods_to_model(model)")
    print("   2. Run trainer test: test_results = trainer.test(model, test_loader)")
    print("   3. Or use comprehensive eval: results = create_comprehensive_evaluation(model, test_loader)")

if __name__ == "__main__":
    print("🔧 Test Step Fix for CELEST AI")
    print("=" * 40)
    print_usage()
